PHP                                                                        NEWS
|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
05 Jun 2025, PHP 8.3.22

- Core:
  . Fixed GH-18480 (array_splice with large values for offset/length arguments).
    (niels<PERSON>/<PERSON>)
  . Partially fixed GH-18572 (nested object comparisons leading to stack overflow).
    (<PERSON>)
  . Fixed OSS-Fuzz #417078295. (nielsdos)
  . Fixed OSS-Fuzz #418106144. (nielsdos)

- Curl:
  . Fixed GH-18460 (curl_easy_setopt with CURLOPT_USERPWD/CURLOPT_USERNAME/
    CURLOPT_PASSWORD set the Authorization header when set to NULL).
    (<PERSON>)

- Date:
  . Fixed bug GH-18076 (Since PHP 8, the date_sun_info() function returns
    inaccurate sunrise and sunset times, but other calculated times are
    correct) (JiriJozif).
  . Fixed bug GH-18481 (date_sunrise with unexpected nan value for the offset).
    (niels<PERSON>/<PERSON>)

- Intl:
  . Fix various reference issues. (nielsdos)

- LDAP:
  . Fixed bug GH-18529 (ldap no longer respects TLS_CACERT from ldaprc in
    ldap_start_tls()). (Remi)

- Opcache:
  . Fixed bug GH-18417 (Windows SHM reattachment fails when increasing
    memory_consumption or jit_buffer_size). (nielsdos)
  . Fixed bug GH-18567 (Preloading with internal class alias triggers assertion
    failure). (nielsdos)
  . Fix leak of accel_globals->key. (nielsdos)

- OpenSSL:
  . Fix missing checks against php_set_blocking() in xp_ssl.c. (nielsdos)

- PDO_OCI:
  . Fixed bug GH-18494 (PDO OCI segfault in statement GC). (nielsdos)

- SPL:
  . Fixed bug GH-18421 (Integer overflow with large numbers in LimitIterator).
    (nielsdos)

- Standard:
  . Fixed bug GH-17403 (Potential deadlock when putenv fails). (nielsdos)
  . Fixed bug GH-18509 (Dynamic calls to assert() ignore zend.assertions).
    (timwolla)

- Windows:
  . Fix leak+crash with sapi_windows_set_ctrl_handler(). (nielsdos)

- Zip:
  . Fixed bug GH-18431 (Registering ZIP progress callback twice doesn't work).
    (nielsdos)
  . Fixed bug GH-18438 (Handling of empty data and errors in
    ZipArchive::addPattern). (nielsdos)

08 May 2025, PHP 8.3.21

- Core:
  . Fixed bug GH-18304 (Changing the properties of a DateInterval through
    dynamic properties triggers a SegFault). (nielsdos)
  . Fix some leaks in php_scandir. (nielsdos)

- Filter:
  . Fixed bug GH-18309 (ipv6 filter integer overflow). (nielsdos)

- GD:
  . Fixed imagecrop() overflow with rect argument with x/width y/heigh usage
    in gdImageCrop(). (David Carlier)
  . Fixed GH-18243 imagettftext() overflow/underflow on font size value.
    (David Carlier)

- Intl:
  . Fix reference support for intltz_get_offset(). (nielsdos)

- LDAP:
  . Fixed bug GH-17776 (LDAP_OPT_X_TLS_* options can't be overridden). (Remi)
  . Fix NULL deref on high modification key. (nielsdos)

- libxml:
  . Fixed custom external entity loader returning an invalid resource leading
    to a confusing TypeError message. (Girgias)

- OpenSSL:
  . Fix memory leak in openssl_sign() when passing invalid algorithm.
    (nielsdos)
  . Fix potential leaks when writing to BIO fails. (nielsdos)

- PDO Firebird:
  . Fixed GH-18276 - persistent connection - "zend_mm_heap corrupted"
    with setAttribute() (SakiTakamachi).

- SPL:
  . Fixed bug GH-18322 (SplObjectStorage debug handler mismanages memory).
    (nielsdos)

- Standard:
  . Fixed bug GH-18145 (php8ts crashes in php_clear_stat_cache()).
    (Jakub Zelenka)
  . Fixed bug GH-18209 (Use-after-free in extract() with EXTR_REFS). (ilutov)
  . Fixed bug GH-18212 (fseek with SEEK_CUR whence value and negative offset
    leads to negative stream position). (David Carlier)
  . Fix resource leak in iptcembed() on error. (nielsdos)

- Zip:
  . Fix uouv when handling empty options in ZipArchive::addGlob(). (nielsdos)
  . Fix memory leak when handling a too long path in ZipArchive::addGlob().
    (nielsdos)

10 Apr 2025, PHP 8.3.20

- Core:
  . Fixed bug GH-17961 (use-after-free during dl()'ed module class destruction).
    (Arnaud)
  . Fixed bug GH-15367 (dl() of module with aliased class crashes in shutdown).
    (Arnaud)
  . Fixed bug GH-13193 again (Significant performance degradation in 'foreach').
    (nielsdos)

- DOM:
  . Fix weird unpack behaviour in DOM. (nielsdos)
  . Fix xinclude destruction of live attributes. (nielsdos)

- Embed:
  . Fixed bug GH-8533 (Unable to link dynamic libphp on Mac). (Kévin Dunglas)

- Fuzzer:
  . Fixed bug GH-18081 (Memory leaks in error paths of fuzzer SAPI).
    (Lung-Alexandra)

- GD:
  . Fixed bug GH-17984 (calls with arguments as array with references).
    (David Carlier)

- Intl:
  . Fix locale_compose and locale_lookup to work with their array argument
    with values as references. (David Carlier)
  . Fix dateformat_format when the time is an array of references.
    (David Carlier)
  . Fix UConverter::transcode with substitutes as references. (David Carlier)

- Mbstring:
  . Fixed bug GH-17989 (mb_output_handler crash with unset
    http_output_conv_mimetypes). (nielsdos)

- Opcache:
  . Fixed bug GH-18112 (NULL access with preloading and INI option). (nielsdos)
  . Fixed bug GH-18107 (Opcache CFG jmp optimization with try-finally breaks
    the exception table). (nielsdos)

- PDO:
  . Fix memory leak when destroying PDORow. (nielsdos)

- SOAP:
  . Fixed bug #66049 (Typemap can break parsing in parse_packet_soap leading to
    a segfault) . (Remi)

- SPL:
  . Fixed bug GH-18018 (RC1 data returned from offsetGet causes UAF in
    ArrayObject). (nielsdos)

- Treewide:
  . Fixed bug GH-17736 (Assertion failure zend_reference_destroy()). (nielsdos)

- Windows:
  . Fixed bug GH-17836 (zend_vm_gen.php shouldn't break on Windows line
    endings). (DanielEScherzer)

13 Mar 2025, PHP 8.3.19

- BCMath:
  . Fixed bug GH-17398 (bcmul memory leak). (SakiTakamachi)

- Core:
  . Fixed bug GH-17623 (Broken stack overflow detection for variable
    compilation). (ilutov)
  . Fixed bug GH-17618 (UnhandledMatchError does not take
    zend.exception_ignore_args=1 into account). (timwolla)
  . Fix fallback paths in fast_long_{add,sub}_function. (nielsdos)
  . Fixed bug GH-17718 (Calling static methods on an interface that has
    `__callStatic` is allowed). (timwolla)
  . Fixed bug GH-17797 (zend_test_compile_string crash on invalid
    script path). (David Carlier)
  . Fixed GHSA-rwp7-7vc6-8477 (Reference counting in php_request_shutdown
    causes Use-After-Free). (CVE-2024-11235) (ilutov)

- DOM:
  . Fixed bug GH-17847 (xinclude destroys live node). (nielsdos)

- FFI:
  . Fix FFI Parsing of Pointer Declaration Lists. (davnotdev)

- FPM:
  . Fixed bug GH-17643 (FPM with httpd ProxyPass encoded PATH_INFO env).
    (Jakub Zelenka)

- GD:
  . Fixed bug GH-17772 (imagepalettetotruecolor crash with memory_limit=2M).
    (David Carlier)

- LDAP:
  . Fixed bug GH-17704 (ldap_search fails when $attributes contains a
    non-packed array with numerical keys). (nielsdos, 7u83)

- LibXML:
  . Fixed GHSA-wg4p-4hqh-c3g9 (Reocurrence of #72714). (nielsdos)
  . Fixed GHSA-p3x9-6h7p-cgfc (libxml streams use wrong `content-type` header
    when requesting a redirected resource). (CVE-2025-1219) (timwolla)

- MBString:
  . Fixed bug GH-17503 (Undefined float conversion in mb_convert_variables).
    (cmb)

- Opcache:
  . Fixed bug GH-17654 (Multiple classes using same trait causes function
    JIT crash). (nielsdos)
  . Fixed bug GH-17577 (JIT packed type guard crash). (nielsdos, Dmitry)
  . Fixed bug GH-17899 (zend_test_compile_string with invalid path
    when opcache is enabled). (David Carlier)
  . Fixed bug GH-17868 (Cannot allocate memory with tracing JIT). (nielsdos)

- PDO_SQLite:
  . Fixed GH-17837 ()::getColumnMeta() on unexecuted statement segfaults).
    (cmb)
  . Fix cycle leak in sqlite3 setAuthorizer(). (nielsdos)

- Phar:
  . Fixed bug GH-17808: PharFileInfo refcount bug. (nielsdos)

- PHPDBG:
  . Partially fixed bug GH-17387 (Trivial crash in phpdbg lexer). (nielsdos)
  . Fix memory leak in phpdbg calling registered function. (nielsdos)

- Reflection:
  . Fixed bug GH-15902 (Core dumped in ext/reflection/php_reflection.c).
    (DanielEScherzer)

- Sockets:
  . Fixed bug GH-17921 (socket_read/socket_recv overflow on buffer size).
    (David Carlier)

- Standard:
  . Fixed bug #72666 (stat cache clearing inconsistent between file:// paths
    and plain paths). (Jakub Zelenka)

- Streams:
  . Fixed bug GH-17650 (realloc with size 0 in user_filters.c). (nielsdos)
  . Fix memory leak on overflow in _php_stream_scandir(). (nielsdos)
  . Fixed GHSA-hgf5-96fm-v528 (Stream HTTP wrapper header check might omit
    basic auth header). (CVE-2025-1736) (Jakub Zelenka)
  . Fixed GHSA-52jp-hrpf-2jff (Stream HTTP wrapper truncate redirect location
    to 1024 bytes). (CVE-2025-1861) (Jakub Zelenka)
  . Fixed GHSA-pcmh-g36c-qc44 (Streams HTTP wrapper does not fail for headers
    without colon). (CVE-2025-1734) (Jakub Zelenka)
  . Fixed GHSA-v8xr-gpvj-cx9g (Header parser of `http` stream wrapper does not
    handle folded headers). (CVE-2025-1217) (Jakub Zelenka)

- Windows:
  . Fixed phpize for Windows 11 (24H2). (bwoebi)
  . Fixed GH-17855 (CURL_STATICLIB flag set even if linked with shared lib).
    (cmb)

- Zlib:
  . Fixed bug GH-17745 (zlib extension incorrectly handles object arguments).
    (nielsdos)
  . Fix memory leak when encoding check fails. (nielsdos)
  . Fix zlib support for large files. (nielsdos)

13 Feb 2025, PHP 8.3.17

- Core:
  . Fixed bug GH-16892 (ini_parse_quantity() fails to parse inputs starting
    with 0x0b). (nielsdos)
  . Fixed bug GH-16886 (ini_parse_quantity() fails to emit warning for 0x+0).
    (nielsdos)
  . Fixed bug GH-17214 (Relax final+private warning for trait methods with
    inherited final). (ilutov)
  . Fixed NULL arithmetic during system program execution on Windows. (cmb,
    nielsdos)
  . Fixed potential OOB when checking for trailing spaces on Windows. (cmb)
  . Fixed bug GH-17408 (Assertion failure Zend/zend_exceptions.c).
    (nielsdos, ilutov)
  . Fix may_have_extra_named_args flag for ZEND_AST_UNPACK. (nielsdos)
  . Fix NULL arithmetic in System V shared memory emulation for Windows. (cmb)

- DOM:
  . Fixed bug GH-17500 (Segfault with requesting nodeName on nameless doctype).
    (nielsdos)

- Enchant:
  . Fix crashes in enchant when passing null bytes. (nielsdos)

- FTP:
  . Fixed bug GH-16800 (ftp functions can abort with EINTR). (nielsdos)

- GD:
  . Fixed bug GH-17349 (Tiled truecolor filling looses single color
    transparency). (cmb)
  . Fixed bug GH-17373 (imagefttext() ignores clipping rect for palette
    images). (cmb)
  . Ported fix for libgd 223 (gdImageRotateGeneric() does not properly
    interpolate). (cmb)

- Intl:
  . Fixed bug GH-11874 (intl causing segfault in docker images). (nielsdos)
  . Fixed bug GH-17469 (UConverter::transcode always emit E_WARNING on
    invalid encoding). (David Carlier)

- Opcache:
  . Fixed bug GH-17307 (Internal closure causes JIT failure). (nielsdos)
  . Fixed bug GH-17564 (Potential UB when reading from / writing to struct
    padding). (ilutov)

- PDO:
  . Fixed a memory leak when the GC is used to free a PDOStatment. (Girgias)
  . Fixed a crash in the PDO Firebird Statement destructor. (nielsdos)
  . Fixed UAFs when changing default fetch class ctor args. (Girgias, nielsdos)

- Phar:
  . Fixed bug GH-17518 (offset overflow phar extractTo()). (nielsdos)

- PHPDBG:
  . Fix crashes in function registration + test. (nielsdos, Girgias)

- Session:
  . Fix type confusion with session SID constant. (nielsdos)
  . Fixed bug GH-17541 (ext/session NULL pointer dereferencement during
    ID reset). (Girgias)

- SimpleXML:
  . Fixed bug GH-17409 (Assertion failure Zend/zend_hash.c:1730). (nielsdos)

- SNMP:
  . Fixed bug GH-17330 (SNMP::setSecurity segfault on closed session).
    (David Carlier)

- SPL:
  . Fixed bug GH-17463 (crash on SplTempFileObject::ftruncate with negative
    value). (David Carlier)

- Zip:
  . Fixed bug GH-17139 (Fix zip_entry_name() crash on invalid entry).
    (nielsdos)

16 Jan 2025, PHP 8.3.16

- Core:
  . Fixed bug GH-17106 (ZEND_MATCH_ERROR misoptimization). (ilutov)
  . Fixed bug GH-17162 (zend_array_try_init() with dtor can cause engine UAF).
    (nielsdos)
  . Fixed bug GH-17101 (AST->string does not reproduce constructor property
    promotion correctly). (nielsdos)
  . Fixed bug GH-17211 (observer segfault on function loaded with dl()).
    (Arnaud)
  . Fixed bug GH-17216 (Trampoline crash on error). (nielsdos)

- Date:
  . Fixed bug GH-14709 DatePeriod::__construct() overflow on recurrences.
    (David Carlier)

- DBA:
  . Skip test if inifile is disabled. (orlitzky)

- DOM:
  . Fixed bug GH-17224 (UAF in importNode). (nielsdos)

- Embed:
  . Make build command for program using embed portable. (dunglas)

- FFI:
  . Fixed bug #79075 (FFI header parser chokes on comments). (nielsdos)
  . Fix memory leak on ZEND_FFI_TYPE_CHAR conversion failure. (nielsdos)
  . Fixed bug GH-16013 and bug #80857 (Big endian issues). (Dmitry, nielsdos)

- Filter:
  . Fixed bug GH-16944 (Fix filtering special IPv4 and IPv6 ranges, by using
    information from RFC 6890). (Derick)

- FPM:
  . Fixed bug GH-13437 (FPM: ERROR: scoreboard: failed to lock (already
    locked)). (Jakub Zelenka)
  . Fixed bug GH-17112 (Macro redefinitions). (cmb, nielsdos)
  . Fixed bug GH-17208 (bug64539-status-json-encoding.phpt fail on 32-bits).
    (nielsdos)

- GD:
  . Fixed bug GH-16255 (Unexpected nan value in ext/gd/libgd/gd_filter.c).
    (nielsdos, cmb)
  . Ported fix for libgd bug 276 (Sometimes pixels are missing when storing
    images as BMPs). (cmb)

- Gettext:
  . Fixed bug GH-17202 (Segmentation fault ext/gettext/gettext.c
    bindtextdomain()). (Michael Orlitzky)

- Iconv:
  . Fixed bug GH-17047 (UAF on iconv filter failure). (nielsdos)

- LDAP:
  . Fixed bug GH-17280 (ldap_search() fails when $attributes array has holes).
    (nielsdos)

- LibXML:
  . Fixed bug GH-17223 (Memory leak in libxml encoding handling). (nielsdos)

- MBString:
  . Fixed bug GH-17112 (Macro redefinitions). (nielsdos, cmb)

- Opcache:
  . opcache_get_configuration() properly reports jit_prof_threshold. (cmb)
  . Fixed bug GH-17246 (GC during SCCP causes segfault). (Dmitry)

- PCNTL:
  . Fix memory leak in cleanup code of pcntl_exec() when a non stringable
    value is encountered past the first entry. (Girgias)

- PgSql:
  . Fixed bug GH-17158 (pg_fetch_result Shows Incorrect ArgumentCountError
    Message when Called With 1 Argument). (nielsdos)
  . Fixed further ArgumentCountError for calls with flexible
    number of arguments. (David Carlier)

- Phar:
  . Fixed bug GH-17137 (Segmentation fault ext/phar/phar.c). (nielsdos)

- SimpleXML:
  . Fixed bug GH-17040 (SimpleXML's unset can break DOM objects). (nielsdos)
  . Fixed bug GH-17153 (SimpleXML crash when using autovivification on
    document). (nielsdos)

- Sockets:
  . Fixed bug GH-16276 (socket_strerror overflow handling with INT_MIN).
    (David Carlier / cmb)
  . Fixed overflow on SO_LINGER values setting, strengthening values check
    on SO_SNDTIMEO/SO_RCVTIMEO for socket_set_option().
    (David Carlier)

- SPL:
  . Fixed bug GH-17225 (NULL deref in spl_directory.c). (nielsdos)

- Streams:
  . Fixed bug GH-17037 (UAF in user filter when adding existing filter name due
    to incorrect error handling). (nielsdos)
  . Fixed bug GH-16810 (overflow on fopen HTTP wrapper timeout value).
    (David Carlier)
  . Fixed bug GH-17067 (glob:// wrapper doesn't cater to CWD for ZTS builds).
    (cmb)

- Windows:
  . Hardened proc_open() against cmd.exe hijacking. (cmb)

- XML:
  . Fixed bug GH-1718 (unreachable program point in zend_hash). (nielsdos)

19 Dec 2024, PHP 8.3.15

- Calendar:
  . Fixed jdtogregorian overflow. (David Carlier)
  . Fixed cal_to_jd julian_days argument overflow. (David Carlier)

- COM:
  . Fixed bug GH-16991 (Getting typeinfo of non DISPATCH variant segfaults).
    (cmb)

- Core:
  . Fail early in *nix configuration build script. (hakre)
  . Fixed bug GH-16727 (Opcache bad signal 139 crash in ZTS bookworm
    (frankenphp)). (nielsdos)
  . Fixed bug GH-16799 (Assertion failure at Zend/zend_vm_execute.h:7469).
    (nielsdos)
  . Fixed bug GH-16630 (UAF in lexer with encoding translation and heredocs).
    (nielsdos)
  . Fix is_zend_ptr() huge block comparison. (nielsdos)
  . Fixed potential OOB read in zend_dirname() on Windows. (cmb)

- Curl:
  . Fixed bug GH-16802 (open_basedir bypass using curl extension). (nielsdos)
  . Fix various memory leaks in curl mime handling. (nielsdos)

- DOM:
  . Fixed bug GH-16777 (Calling the constructor again on a DOM object after it
    is in a document causes UAF). (nielsdos)
  . Fixed bug GH-16906 (Reloading document can cause UAF in iterator).
    (nielsdos)

- FPM:
  . Fixed GH-16432 (PHP-FPM 8.2 SIGSEGV in fpm_get_status). (Jakub Zelenka)

- GD:
  . Fixed GH-16776 (imagecreatefromstring overflow). (David Carlier)

- GMP:
  . Fixed bug GH-16890 (array_sum() with GMP can loose precision (LLP64)).
    (cmb)

- Hash:
  . Fixed GH-16711: Segfault in mhash(). (Girgias)

- Opcache:
  . Fixed bug GH-16770 (Tracing JIT type mismatch when returning UNDEF).
    (nielsdos, Dmitry)
  . Fixed bug GH-16851 (JIT_G(enabled) not set correctly on other threads).
    (dktapps)
  . Fixed bug GH-16902 (Set of opcache tests fail zts+aarch64). (nielsdos)

- OpenSSL:
  . Prevent unexpected array entry conversion when reading key. (nielsdos)
  . Fix various memory leaks related to openssl exports. (nielsdos)
  . Fix memory leak in php_openssl_pkey_from_zval(). (nielsdos)

- PDO:
  . Fixed memory leak of `setFetchMode()`. (SakiTakamachi)

- Phar:
  . Fixed bug GH-16695 (phar:// tar parser and zero-length file header blocks).
    (nielsdos, Hans Krentel)

- PHPDBG:
  . Fixed bug GH-15208 (Segfault with breakpoint map and phpdbg_clear()).
    (nielsdos)

- SAPI:
  . Fixed bug GH-16998 (UBSAN warning in rfc1867). (nielsdos)

- SimpleXML:
  . Fixed bug GH-16808 (Segmentation fault in RecursiveIteratorIterator
    ->current() with a xml element input). (nielsdos)

- SOAP:
  . Fix make check being invoked in ext/soap. (Ma27)

- Standard:
  . Fixed bug GH-16905 (Internal iterator functions can't handle UNDEF
    properties). (nielsdos)
  . Fixed bug GH-16957 (Assertion failure in array_shift with
    self-referencing array). (nielsdos)

- Streams:
  . Fixed network connect poll interuption handling. (Jakub Zelenka)

- Windows:
  . Fixed bug GH-16849 (Error dialog causes process to hang). (cmb)

21 Nov 2024, PHP 8.3.14

- CLI:
  . Fixed bug GH-16373 (Shebang is not skipped for router script in cli-server
    started through shebang). (ilutov)
  . Fixed bug GHSA-4w77-75f9-2c8w (Heap-Use-After-Free in sapi_read_post_data
    Processing in CLI SAPI Interface). (nielsdos)

- COM:
  . Fixed out of bound writes to SafeArray data. (cmb)

- Core:
  . Fixed bug GH-16168 (php 8.1 and earlier crash immediately when compiled
    with Xcode 16 clang on macOS 15). (nielsdos)
  . Fixed bug GH-16371 (Assertion failure in Zend/zend_weakrefs.c:646). (Arnaud)
  . Fixed bug GH-16515 (Incorrect propagation of ZEND_ACC_RETURN_REFERENCE for
    call trampoline). (ilutov)
  . Fixed bug GH-16509 (Incorrect line number in function redeclaration error).
    (ilutov)
  . Fixed bug GH-16508 (Incorrect line number in inheritance errors of delayed
    early bound classes). (ilutov)
  . Fixed bug GH-16648 (Use-after-free during array sorting). (ilutov)
  . Fixed bug GH-15915 (overflow with a high value for precision INI).
    (David Carlier / cmb)

- Curl:
  . Fixed bug GH-16302 (CurlMultiHandle holds a reference to CurlHandle if
    curl_multi_add_handle fails). (timwolla)

- Date:
  . Fixed bug GH-16454 (Unhandled INF in date_sunset() with tiny $utcOffset).
    (cmb)
  . Fixed bug GH-14732 (date_sun_info() fails for non-finite values). (cmb)

- DBA:
  . Fixed bug GH-16390 (dba_open() can segfault for "pathless" streams). (cmb)

- DOM:
  . Fixed bug GH-16316 (DOMXPath breaks when not initialized properly).
    (nielsdos)
  . Add missing hierarchy checks to replaceChild. (nielsdos)
  . Fixed bug GH-16336 (Attribute intern document mismanagement). (nielsdos)
  . Fixed bug GH-16338 (Null-dereference in ext/dom/node.c). (nielsdos)
  . Fixed bug GH-16473 (dom_import_simplexml stub is wrong). (nielsdos)
  . Fixed bug GH-16533 (Segfault when adding attribute to parent that is not
    an element). (nielsdos)
  . Fixed bug GH-16535 (UAF when using document as a child). (nielsdos)
  . Fixed bug GH-16593 (Assertion failure in DOM->replaceChild). (nielsdos)
  . Fixed bug GH-16595 (Another UAF in DOM -> cloneNode). (nielsdos)

- EXIF:
  . Fixed bug GH-16409 (Segfault in exif_thumbnail when not dealing with a
    real file). (nielsdos, cmb)

- FFI:
  . Fixed bug GH-16397 (Segmentation fault when comparing FFI object).
    (nielsdos)

- Filter:
  . Fixed bug GH-16523 (FILTER_FLAG_HOSTNAME accepts ending hyphen). (cmb)

- FPM:
  . Fixed bug GH-16628 (FPM logs are getting corrupted with this log
    statement). (nielsdos)

- GD:
  . Fixed bug GH-16334 (imageaffine overflow on matrix elements).
    (David Carlier)
  . Fixed bug GH-16427 (Unchecked libavif return values). (cmb)
  . Fixed bug GH-16559 (UBSan abort in ext/gd/libgd/gd_interpolation.c:1007).
    (nielsdos)

- GMP:
  . Fixed floating point exception bug with gmp_pow when using
    large exposant values. (David Carlier).
  . Fixed bug GH-16411 (gmp_export() can cause overflow). (cmb)
  . Fixed bug GH-16501 (gmp_random_bits() can cause overflow).
    (David Carlier)
  . Fixed gmp_pow() overflow bug with large base/exponents.
    (David Carlier)
  . Fixed segfaults and other issues related to operator overloading with
    GMP objects. (Girgias)

- LDAP:
  . Fixed bug GHSA-g665-fm4p-vhff (OOB access in ldap_escape). (CVE-2024-8932)
    (nielsdos)

- MBstring:
  . Fixed bug GH-16361 (mb_substr overflow on start/length arguments).
    (David Carlier)

- MySQLnd:
  . Fixed bug GHSA-h35g-vwh6-m678 (Leak partial content of the heap through
    heap buffer over-read). (CVE-2024-8929) (Jakub Zelenka)

- Opcache:
  . Fixed bug GH-16408 (Array to string conversion warning emitted in
    optimizer). (ilutov)

- OpenSSL:
  . Fixed bug GH-16357 (openssl may modify member types of certificate arrays).
    (cmb)
  . Fixed bug GH-16433 (Large values for openssl_csr_sign() $days overflow).
    (cmb)
  . Fix various memory leaks on error conditions in openssl_x509_parse().
    (nielsdos)

- PDO DBLIB:
  . Fixed bug GHSA-5hqh-c84r-qjcv (Integer overflow in the dblib quoter causing
    OOB writes). (CVE-2024-11236) (nielsdos)

- PDO Firebird:
  . Fixed bug GHSA-5hqh-c84r-qjcv (Integer overflow in the firebird quoter
    causing OOB writes). (CVE-2024-11236) (nielsdos)

- PDO ODBC:
  . Fixed bug GH-16450 (PDO_ODBC can inject garbage into field values). (cmb)

- Phar:
  . Fixed bug GH-16406 (Assertion failure in ext/phar/phar.c:2808). (nielsdos)

- PHPDBG:
  . Fixed bug GH-16174 (Empty string is an invalid expression for ev). (cmb)

- Reflection:
  . Fixed bug GH-16601 (Memory leak in Reflection constructors). (nielsdos)

- Session:
  . Fixed bug GH-16385 (Unexpected null returned by session_set_cookie_params).
    (nielsdos)
  . Fixed bug GH-16290 (overflow on cookie_lifetime ini value).
    (David Carlier)

- SOAP:
  . Fixed bug GH-16318 (Recursive array segfaults soap encoding). (nielsdos)
  . Fixed bug GH-16429 (Segmentation fault access null pointer in SoapClient).
    (nielsdos)

- Sockets:
  . Fixed bug with overflow socket_recvfrom $length argument. (David Carlier)

- SPL:
  . Fixed bug GH-16337 (Use-after-free in SplHeap). (nielsdos)
  . Fixed bug GH-16464 (Use-after-free in SplDoublyLinkedList::offsetSet()).
    (ilutov)
  . Fixed bug GH-16479 (Use-after-free in SplObjectStorage::setInfo()). (ilutov)
  . Fixed bug GH-16478 (Use-after-free in SplFixedArray::unset()). (ilutov)
  . Fixed bug GH-16588 (UAF in Observer->serialize). (nielsdos)
  . Fix GH-16477 (Segmentation fault when calling __debugInfo() after failed
    SplFileObject::__constructor). (Girgias)
  . Fixed bug GH-16589 (UAF in SplDoublyLinked->serialize()). (nielsdos)
  . Fixed bug GH-14687 (segfault on SplObjectIterator instance).
    (David Carlier)
  . Fixed bug GH-16604 (Memory leaks in SPL constructors). (nielsdos)
  . Fixed bug GH-16646 (UAF in ArrayObject::unset() and
    ArrayObject::exchangeArray()). (ilutov)

- Standard:
  . Fixed bug GH-16293 (Failed assertion when throwing in assert() callback with
    bail enabled). (ilutov)

- Streams:
  . Fixed bug GHSA-c5f2-jwm7-mmq2 (Configuring a proxy in a stream context
    might allow for CRLF injection in URIs). (CVE-2024-11234) (Jakub Zelenka)
  . Fixed bug GHSA-r977-prxv-hc43 (Single byte overread with
    convert.quoted-printable-decode filter). (CVE-2024-11233) (nielsdos)

- SysVMsg:
  . Fixed bug GH-16592 (msg_send() crashes when a type does not properly
    serialized). (David Carlier / cmb)

- SysVShm:
  . Fixed bug GH-16591 (Assertion error in shm_put_var). (nielsdos, cmb)

- XMLReader:
  . Fixed bug GH-16292 (Segmentation fault in ext/xmlreader/php_xmlreader.c).
    (nielsdos)

- Zlib:
  . Fixed bug GH-16326 (Memory management is broken for bad dictionaries.)
    (cmb)

24 Oct 2024, PHP 8.3.13

- Calendar:
  . Fixed GH-16240: jdtounix overflow on argument value. (David Carlier)
  . Fixed GH-16241: easter_days/easter_date overflow on year argument.
    (David Carlier)
  . Fixed GH-16263: jddayofweek overflow. (cmb)
  . Fixed GH-16234: jewishtojd overflow. (nielsdos)

- CLI:
  . Fixed bug GH-16137: duplicate http headers when set several times by
    the client. (David Carlier)

- Core:
  . Fixed bug GH-16054 (Segmentation fault when resizing hash table iterator
    list while adding). (nielsdos)
  . Fixed bug GH-15905 (Assertion failure for TRACK_VARS_SERVER). (cmb)
  . Fixed bug GH-15907 (Failed assertion when promoting Serialize deprecation to
    exception). (ilutov)
  . Fixed bug GH-15851 (Segfault when printing backtrace during cleanup of
    nested generator frame). (ilutov)
  . Fixed bug GH-15866 (Core dumped in Zend/zend_generators.c). (Arnaud)
  . Fixed bug GH-16188 (Assertion failure in Zend/zend_exceptions.c). (Arnaud)
  . Fixed bug GH-16233 (Observer segfault when calling user function in
    internal function via trampoline). (nielsdos)

- DOM:
  . Fixed bug GH-16039 (Segmentation fault (access null pointer) in
    ext/dom/parentnode/tree.c). (nielsdos)
  . Fixed bug GH-16149 (Null pointer dereference in
    DOMElement->getAttributeNames()). (nielsdos)
  . Fixed bug GH-16151 (Assertion failure in ext/dom/parentnode/tree.c).
    (nielsdos)
  . Fixed bug GH-16150 (Use after free in php_dom.c). (nielsdos)
  . Fixed bug GH-16152 (Memory leak in DOMProcessingInstruction/DOMDocument).
    (nielsdos)

- JSON:
  . Fixed bug GH-15168 (stack overflow in json_encode()). (nielsdos)

- GD:
  . Fixed bug GH-16232 (bitshift overflow on wbmp file content reading /
    fix backport from upstream). (David Carlier)
  . Fixed bug GH-12264 (overflow/underflow on imagerotate degrees value)
    (David Carlier)
  . Fixed bug GH-16274 (imagescale underflow on RBG channels /
    fix backport from upstream). (David Carlier)

- LDAP:
  . Fixed bug GH-16032 (Various NULL pointer dereferencements in
    ldap_modify_batch()). (Girgias)
  . Fixed bug GH-16101 (Segfault in ldap_list(), ldap_read(), and ldap_search()
    when LDAPs array is not a list). (Girgias)
  . Fix GH-16132 (php_ldap_do_modify() attempts to free pointer not allocated
    by ZMM.). (Girgias)
  . Fix GH-16136 (Memory leak in php_ldap_do_modify() when entry is not a
    proper dictionary). (Girgias)

- MBString:
  . Fixed bug GH-16261 (Reference invariant broken in mb_convert_variables()).
    (nielsdos)

- OpenSSL:
  . Fixed stub for openssl_csr_new. (Jakub Zelenka)

- PCRE:
  . Fixed bug GH-16189 (underflow on offset argument). (David Carlier)
  . Fixed bug GH-16184 (UBSan address overflowed in ext/pcre/php_pcre.c).
    (nielsdos)

- PHPDBG:
  . Fixed bug GH-15901 (phpdbg: Assertion failure on i funcs). (cmb)
  . Fixed bug GH-16181 (phpdbg: exit in exception handler reports fatal error).
    (cmb)

- Reflection:
  . Fixed bug GH-16187 (Assertion failure in ext/reflection/php_reflection.c).
    (DanielEScherzer)

- SAPI:
  . Fixed bug GH-15395 (php-fpm: zend_mm_heap corrupted with cgi-fcgi request).
    (Jakub Zelenka, David Carlier)

- SimpleXML:
  . Fixed bug GH-15837 (Segmentation fault in ext/simplexml/simplexml.c).
    (nielsdos)

- Sockets:
  . Fixed bug GH-16267 (socket_strerror overflow on errno argument).
    (David Carlier)

- SOAP:
  . Fixed bug #73182 (PHP SOAPClient does not support stream context HTTP
    headers in array form). (nielsdos)
  . Fixed bug #62900 (Wrong namespace on xsd import error message). (nielsdos)
  . Fixed bug GH-15711 (SoapClient can't convert BackedEnum to scalar value).
    (nielsdos)
  . Fixed bug GH-16237 (Segmentation fault when cloning SoapServer). (nielsdos)
  . Fix Soap leaking http_msg on error. (nielsdos)
  . Fixed bug GH-16256 (Assertion failure in ext/soap/php_encoding.c:460).
    (nielsdos)
  . Fixed bug GH-16259 (Soap segfault when classmap instantiation fails).
    (nielsdos)

- SPL:
  . Fixed bug GH-15918 (Assertion failure in ext/spl/spl_fixedarray.c).
    (nielsdos)

- Standard:
  . Fixed bug GH-16053 (Assertion failure in Zend/zend_hash.c). (Arnaud)
  . Fixed bug GH-15169 (stack overflow when var serialization in
    ext/standard/var). (nielsdos)

- Streams:
  . Fixed bugs GH-15908 and GH-15026 (leak / assertion failure in streams.c).
    (nielsdos)
  . Fixed bug GH-15980 (Signed integer overflow in main/streams/streams.c).
    (cmb)

- TSRM:
  . Prevent closing of unrelated handles. (cmb)

- Windows:
  . Fixed minimal Windows version. (cmb)

26 Sep 2024, PHP 8.3.12

- CGI:
  . Fixed bug GHSA-p99j-rfp4-xqvq (Bypass of CVE-2024-4577, Parameter Injection
    Vulnerability). (CVE-2024-8926) (nielsdos)
  . Fixed bug GHSA-94p6-54jq-9mwp (cgi.force_redirect configuration is
    bypassable due to the environment variable collision). (CVE-2024-8927)
    (nielsdos)

- Core:
  . Fixed bug GH-15408 (MSan false-positve on zend_max_execution_timer).
    (zeriyoshi)
  . Fixed bug GH-15515 (Configure error grep illegal option q). (Peter Kokot)
  . Fixed bug GH-15514 (Configure error: genif.sh: syntax error). (Peter Kokot)
  . Fixed bug GH-15565 (--disable-ipv6 during compilation produces error
    EAI_SYSTEM not found). (nielsdos)
  . Fixed bug GH-15587 (CRC32 API build error on arm 32-bit).
    (Bernd Kuhls, Thomas Petazzoni)
  . Fixed bug GH-15330 (Do not scan generator frames more than once). (Arnaud)
  . Fixed uninitialized lineno in constant AST of internal enums. (ilutov)

- Curl:
  . FIxed bug GH-15547 (curl_multi_select overflow on timeout argument).
    (David Carlier)

- DOM:
  . Fixed bug GH-15551 (Segmentation fault (access null pointer) in
    ext/dom/xml_common.h). (nielsdos)
  . Fixed bug GH-15654 (Signed integer overflow in ext/dom/nodelist.c).
    (nielsdos)

- Fileinfo:
  . Fixed bug GH-15752 (Incorrect error message for finfo_file
    with an empty filename argument). (DanielEScherzer)

- FPM:
  . Fixed bug GHSA-865w-9rf3-2wh5 (Logs from childrens may be altered).
    (CVE-2024-9026) (Jakub Zelenka)

- MySQLnd:
  . Fixed bug GH-15432 (Heap corruption when querying a vector). (cmb,
    Kamil Tekiela)

- Opcache:
  . Fixed bug GH-15661 (Access null pointer in
    Zend/Optimizer/zend_inference.c). (nielsdos)
  . Fixed bug GH-15658 (Segmentation fault in Zend/zend_vm_execute.h).
    (nielsdos)

- SAPI:
  . Fixed bug GHSA-9pqp-7h25-4f32 (Erroneous parsing of multipart form data).
    (CVE-2024-8925) (Arnaud)

- Standard:
  . Fixed bug GH-15552 (Signed integer overflow in ext/standard/scanf.c). (cmb)

- Streams:
  . Fixed bug GH-15628 (php_stream_memory_get_buffer() not zero-terminated).
    (cmb)

29 Aug 2024, PHP 8.3.11

- Core:
  . Fixed bug GH-15020 (Memory leak in Zend/Optimizer/escape_analysis.c).
    (nielsdos)
  . Fixed bug GH-15023 (Memory leak in Zend/zend_ini.c). (nielsdos)
  . Fixed bug GH-13330 (Append -Wno-implicit-fallthrough flag conditionally).
    (Peter Kokot)
  . Fix uninitialized memory in network.c. (nielsdos)
  . Fixed bug GH-15108 (Segfault when destroying generator during shutdown).
    (Arnaud)
  . Fixed bug GH-15275 (Crash during GC of suspended generator delegate).
    (Arnaud)

- Curl:
  . Fixed case when curl_error returns an empty string.
    (David Carlier)

- DOM:
  . Fix UAF when removing doctype and using foreach iteration. (nielsdos)

- FFI:
  . Fixed bug GH-14286 (ffi enum type (when enum has no name) make memory
    leak). (nielsdos, dstogov)

- Hash:
  . Fix crash when converting array data for array in shm in xxh3. (nielsdos)

- Intl:
  . Fixed bug GH-15087 (IntlChar::foldCase()'s $option is not optional). (cmb)

- Opcache:
  . Fixed bug GH-13817 (Segmentation fault for enabled observers after pass 4).
    (Bob)
  . Fixed bug GH-13775 (Memory leak possibly related to opcache SHM placement).
    (Arnaud, nielsdos)

- Output:
  . Fixed bug GH-15179 (Segmentation fault (null pointer dereference) in
    ext/standard/url_scanner_ex.re). (nielsdos)

- PDO_Firebird:
  . Fix bogus fallthrough path in firebird_handle_get_attribute(). (nielsdos)

- PHPDBG:
  . Fixed bug GH-13199 (EOF emits redundant prompt in phpdbg local console mode
    with libedit/readline). (Peter Kokot)
  . Fixed bug GH-15268 (heap buffer overflow in phpdbg
    (zend_hash_num_elements() Zend/zend_hash.h)). (nielsdos)
  . Fixed bug GH-15210 use-after-free on watchpoint allocations. (nielsdos)

- Random:
  . Fixed part of bug GH-15381, checking getrandom availability on solaris.
    (David Carlier)

- Soap:
  . Fixed bug #55639 (Digest autentication dont work). (nielsdos)
  . Fix SoapFault property destruction. (nielsdos)
  . Fixed bug GH-15252 (SOAP XML broken since PHP 8.3.9 when using classmap
    constructor option). (nielsdos)

- Standard:
  . Fix passing non-finite timeout values in stream functions. (nielsdos)
  . Fixed GH-14780 p(f)sockopen timeout overflow. (David Carlier)
  . Fixed GH-15653 overflow on fgetcsv length parameter. (David Carlier)

- Streams:
  . Fixed bug GH-15028 (Memory leak in ext/phar/stream.c). (nielsdos)
  . Fixed bug GH-15034 (Integer overflow on stream_notification_callback
    byte_max parameter with files bigger than 2GB). (nielsdos)
  . Reverted fix for GH-14930 (Custom stream wrapper dir_readdir output
    truncated to 255 characters). (Jakub Zelenka)

- Tidy:
  . Fix memory leaks in ext/tidy basedir restriction code. (nielsdos)

01 Aug 2024, PHP 8.3.10

- Core:
  . Fixed bug GH-13922 (Fixed support for systems with
    sysconf(_SC_GETPW_R_SIZE_MAX) == -1). (Arnaud)
  . Fixed bug GH-14626 (Fix is_zend_ptr() for huge blocks). (Arnaud)
  . Fixed bug GH-14590 (Memory leak in FPM test gh13563-conf-bool-env.phpt.
    (nielsdos)
  . Fixed OSS-Fuzz #69765. (nielsdos)
  . Fixed bug GH-14741 (Segmentation fault in Zend/zend_types.h). (nielsdos)
  . Fixed bug GH-14969 (Use-after-free in property coercion with __toString()).
    (ilutov)
  . Fixed bug GH-14961 (Comment between -> and keyword results in parse error).
    (ilutov)

- Dom:
  . Fixed bug GH-14702 (DOMDocument::xinclude() crash). (nielsdos)

- Fileinfo:
  . Fixed bug GH-14888 (README.REDIST.BINS refers to non-existing LICENSE).
    (cmb)

- Gd:
  . ext/gd/tests/gh10614.phpt: skip if no PNG support. (orlitzky)
  . restored warning instead of fata error. (dryabov)

- LibXML:
  . Fixed bug GH-14563 (Build failure with libxml2 v2.13.0). (nielsdos)

- Opcache:
  . Fixed bug GH-14550 (No warning message when Zend DTrace is enabled that
    opcache.jit is implictly disabled). (nielsdos)

- Output:
  . Fixed bug GH-14808 (Unexpected null pointer in Zend/zend_string.h with
    empty output buffer). (nielsdos)

- PDO:
  . Fixed bug GH-14712 (Crash with PDORow access to null property).
    (David Carlier)

- Phar:
  . Fixed bug GH-14603 (null string from zip entry).
    (David Carlier)

- PHPDBG:
  . Fixed bug GH-14596 (crashes with ASAN and ZEND_RC_DEBUG=1).
    (David Carlier)
  . Fixed bug GH-14553 (echo output trimmed at NULL byte). (nielsdos)

- Shmop:
  . Fixed bug GH-14537 (shmop Windows 11 crashes the process). (nielsdos)

- SPL:
  . Fixed bug GH-14639 (Member access within null pointer in
    ext/spl/spl_observer.c). (nielsdos)

- Standard:
  . Fixed bug GH-14775 (range function overflow with negative step argument).
    (David Carlier)
  . Fix 32-bit wordwrap test failures. (orlitzky)
  . Fixed bug GH-14774 (time_sleep_until overflow). (David Carlier)

- Streams:
  . Fixed bug GH-14930 (Custom stream wrapper dir_readdir output truncated to
    255 characters in PHP 8.3). (Joe Cai)

- Tidy:
  . Fix memory leak in tidy_repair_file(). (nielsdos)

- Treewide:
  . Fix compatibility with libxml2 2.13.2. (nielsdos)

- XML:
  . Move away from to-be-deprecated libxml fields. (nielsdos)
  . Fixed bug GH-14834 (Error installing PHP when --with-pear is used).
    (nielsdos)

04 Jul 2024, PHP 8.3.9

- Core:
  . Fixed bug GH-14315 (Incompatible pointer type warnings). (Peter Kokot)
  . Fixed bug GH-12814 (max_execution_time reached too early on MacOS 14
    when running on Apple Silicon). (Manuel Kress)
  . Fixed bug GH-14387 (Crash when stack walking in destructor of yielded from
    values during Generator->throw()). (Bob)
  . Fixed bug GH-14456 (Attempting to initialize class with private constructor
    calls destructor). (Girgias)
  . Fixed bug GH-14510 (memleak due to missing pthread_attr_destroy()-call).
    (Florian Engelhardt)
  . Fixed bug GH-14549 (Incompatible function pointer type for fclose).
    (Ryan Carsten Schmidt)

- BCMath:
  . Fixed bug (bcpowmod() with mod = -1 returns 1 when it must be 0). (Girgias)

- Curl:
  . Fixed bug GH-14307 (Test curl_basic_024 fails with curl 8.8.0). (nielsdos)

- DOM:
  . Fixed bug GH-14343 (Memory leak in xml and dom). (nielsdos)

- FPM:
  . Fixed bug GH-14037 (PHP-FPM ping.path and ping.response config vars are
    ignored in status pool). (Wilhansen Li, Pierrick Charron)

- GD:
  . Fix parameter numbers for imagecolorset(). (Giovanni Giacobbi)

- Intl:
  . Fix reference handling in SpoofChecker. (nielsdos)

- MySQLnd:
  . Partially fix bug GH-10599 (Apache crash on Windows when using a
    self-referencing anonymous function inside a class with an active
    mysqli connection). (nielsdos)

- Opcache:
  . Fixed bug GH-14267 (opcache.jit=off does not allow enabling JIT at runtime).
    (ilutov)
  . Fixed TLS access in JIT on FreeBSD/amd64. (Arnaud)
  . Fixed bug GH-11188 (Error when building TSRM in ARM64). (nielsdos)

- PDO ODBC:
  . Fixed bug GH-14367 (incompatible SDWORD type with iODBC). (Calvin Buckley)

- PHPDBG:
  . Fixed bug GH-13681 (segfault on watchpoint addition failure). (David Carlier)

- Soap:
  . Fixed bug #47925 (PHPClient can't decompress response). (nielsdos)
  . Fix missing error restore code. (nielsdos)
  . Fix memory leak if calling SoapServer::setObject() twice. (nielsdos)
  . Fix memory leak if calling SoapServer::setClass() twice. (nielsdos)
  . Fix reading zlib ini settings in ext-soap. (nielsdos)
  . Fix memory leaks with string function name lookups. (nielsdos)
  . Fixed bug #69280 (SoapClient classmap doesn't support fully qualified class
    name). (nielsdos)
  . Fixed bug #76232 (SoapClient Cookie Header Semicolon). (nielsdos)
  . Fixed memory leaks when calling SoapFault::__construct() twice. (Girgias)

- Sodium:
  . Fix memory leaks in ext/sodium on failure of some functions. (nielsdos)

- SPL:
  . Fixed bug GH-14290 (Member access within null pointer in extension spl).
    (nielsdos)

- Standard:
  . Fixed bug GH-14483 (Fixed off-by-one error in checking length of abstract
    namespace Unix sockets). (Derick)

- Streams:
  . Fixed bug GH-11078 (PHP Fatal error triggers pointer being freed was not
    allocated and malloc: double free for ptr errors). (nielsdos)

06 Jun 2024, PHP 8.3.8

- CGI:
  . Fixed buffer limit on Windows, replacing read call usage by _read.
    (David Carlier)
  . Fixed bug GHSA-3qgc-jrrr-25jv (Bypass of CVE-2012-1823, Argument Injection
    in PHP-CGI). (CVE-2024-4577) (nielsdos)

- CLI:
  . Fixed bug GH-14189 (PHP Interactive shell input state incorrectly handles
    quoted heredoc literals.). (nielsdos)

- Core:
  . Fixed bug GH-13970 (Incorrect validation of #[Attribute] flags type for
    non-compile-time expressions). (ilutov)

- DOM:
  . Fix crashes when entity declaration is removed while still having entity
    references. (nielsdos)
  . Fix references not handled correctly in C14N. (nielsdos)
  . Fix crash when calling childNodes next() when iterator is exhausted.
    (nielsdos)
  . Fix crash in ParentNode::append() when dealing with a fragment
    containing text nodes. (nielsdos)

- Filter:
  . Fixed bug GHSA-w8qr-v226-r27w (Filter bypass in filter_var FILTER_VALIDATE_URL).
    (CVE-2024-5458) (nielsdos)

- FPM:
  . Fix bug GH-14175 (Show decimal number instead of scientific notation in
    systemd status). (Benjamin Cremer)

- Hash:
  . ext/hash: Swap the checking order of `__has_builtin` and `__GNUC__`
    (Saki Takamachi)

- Intl:
  . Fixed build regression on systems without C++17 compilers. (Calvin Buckley,
    Peter Kokot)

- MySQLnd:
  . Fix bug GH-14255 (mysqli_fetch_assoc reports error from
    nested query). (Kamil Tekiela)

- Opcache:
  . Fixed bug GH-14109 (Fix accidental persisting of internal class constant in
    shm). (ilutov)

- OpenSSL:
  . The openssl_private_decrypt function in PHP, when using PKCS1 padding
    (OPENSSL_PKCS1_PADDING, which is the default), is vulnerable to the Marvin Attack
    unless it is used with an OpenSSL version that includes the changes from this pull
    request: https://github.com/openssl/openssl/pull/13817 (rsa_pkcs1_implicit_rejection).
    These changes are part of OpenSSL 3.2 and have also been backported to stable
    versions of various Linux distributions, as well as to the PHP builds provided for
    Windows since the previous release. All distributors and builders should ensure that
    this version is used to prevent PHP from being vulnerable. (CVE-2024-2408)

- Standard:
  . Fixed bug GHSA-9fcc-425m-g385 (Bypass of CVE-2024-1874).
    (CVE-2024-5585) (nielsdos)

- XML:
  . Fixed bug GH-14124 (Segmentation fault with XML extension under certain
    memory limit). (nielsdos)

- XMLReader:
  . Fixed bug GH-14183 (XMLReader::open() can't be overridden). (nielsdos)

09 May 2024, PHP 8.3.7

- Core:
  . Fixed zend_call_stack build with Linux/uclibc-ng without thread support.
    (Fabrice Fontaine)
  . Fixed bug GH-13772 (Invalid execute_data->opline pointers in observer fcall
    handlers when JIT is enabled). (Bob)
  . Fixed bug GH-13931 (Applying zero offset to null pointer in
    Zend/zend_opcode.c). (nielsdos)
  . Fixed bug GH-13942 (Align the behavior of zend-max-execution-timers with
    other timeout implementations). (Kévin Dunglas)
  . Fixed bug GH-14003 (Broken cleanup of unfinished calls with callable convert
    parameters). (ilutov)
  . Fixed bug GH-14013 (Erroneous dnl appended in configure). (Peter Kokot)
  . Fixed bug GH-10232 (If autoloading occurs during constant resolution
    filename and lineno are identified incorrectly). (ranvis)
  . Fixed bug GH-13727 (Missing void keyword). (Peter Kokot)

- Fibers:
  . Fixed bug GH-13903 (ASAN false positive underflow when executing copy()).
    (nielsdos)

- Fileinfo:
  . Fixed bug GH-13795 (Test failing in ext/fileinfo/tests/bug78987.phpt on
    big-endian PPC). (orlitzky)

- FPM:
  . Fixed bug GH-13563 (Setting bool values via env in FPM config fails).
    (Jakub Zelenka)

- Intl:
  . Fixed build for icu 74 and onwards. (dunglas)

- MySQLnd:
  . Fix shift out of bounds on 32-bit non-fast-path platforms. (nielsdos)

- Opcache:
  . Fixed bug GH-13433 (Segmentation Fault in zend_class_init_statics when
    using opcache.preload). (nielsdos)
  . Fixed incorrect assumptions across compilation units for static calls.
    (ilutov)

- OpenSSL:
  . Fixed bug GH-10495 (feof on OpenSSL stream hangs indefinitely).
    (Jakub Zelenka)

- PDO SQLite:
  . Fix GH-13984 (Buffer size is now checked before memcmp). (Saki Takamachi)
  . Fix GH-13998 (Manage refcount of agg_context->val correctly).
    (Saki Takamachi)

- Phar:
  . Fixed bug GH-13836 (Renaming a file in a Phar to an already existing
    filename causes a NULL pointer dereference). (nielsdos)
  . Fixed bug GH-13833 (Applying zero offset to null pointer in zend_hash.c).
    (nielsdos)
  . Fix potential NULL pointer dereference before calling EVP_SignInit. (icy17)

- PHPDBG:
  . Fixed bug GH-13827 (Null pointer access of type 'zval' in phpdbg_frame).
    (nielsdos)

- Posix:
  . Fix usage of reentrant functions in ext/posix. (Arnaud)

- Session:
  . Fixed bug GH-13856 (Member access within null pointer of type 'ps_files' in
    ext/session/mod_files.c). (nielsdos)
  . Fixed bug GH-13891 (memleak and segfault when using ini_set with
    session.trans_sid_hosts). (nielsdos, kamil-tekiela)
  . Fixed buffer _read/_write size limit on windows for the file mode. (David Carlier)

- Streams:
  . Fixed file_get_contents() on Windows fails with "errno=22 Invalid
    argument". (Damian Wójcik)
  . Fixed bug GH-13264 (Part 1 - Memory leak on stream filter failure).
    (Jakub Zelenka)
  . Fixed bug GH-13860 (Incorrect PHP_STREAM_OPTION_CHECK_LIVENESS case in
    ext/openssl/xp_ssl.c - causing use of dead socket). (nielsdos)
  . Fixed bug GH-11678 (Build fails on musl 1.2.4 - lfs64). (Arnaud)

- Treewide:
  . Fix gcc-14 Wcalloc-transposed-args warnings. (Cristian Rodríguez)

11 Apr 2024, PHP 8.3.6

- Core:
  . Fixed GH-13569 (GC buffer unnecessarily grows up to GC_MAX_BUF_SIZE when
    scanning WeakMaps). (Arnaud)
  . Fixed bug GH-13612 (Corrupted memory in destructor with weak references).
    (nielsdos)
  . Fixed bug GH-13446 (Restore exception handler after it finishes). (ilutov)
  . Fixed bug GH-13784 (AX_GCC_FUNC_ATTRIBUTE failure). (Remi)
  . Fixed bug GH-13670 (GC does not scale well with a lot of objects created in
    destructor). (Arnaud)

- DOM:
  . Add some missing ZPP checks. (nielsdos)
  . Fix potential memory leak in XPath evaluation results. (nielsdos)

- FPM:
  . Fixed GH-11086 (FPM: config test runs twice in daemonised mode).
    (Jakub Zelenka)
  . Fixed incorrect check in fpm_shm_free(). (nielsdos)

- GD:
  . Fixed bug GH-12019 (add GDLIB_CFLAGS in feature tests). (Michael Orlitzky)

- Gettext:
  . Fixed sigabrt raised with dcgettext/dcngettext calls with gettext 0.22.5
    with category set to LC_ALL. (David Carlier)

- MySQLnd:
  . Fix GH-13452 (Fixed handshake response [mysqlnd]). (Saki Takamachi)
  . Fix incorrect charset length in check_mb_eucjpms(). (nielsdos)

- Opcache:
  . Fixed GH-13508 (JITed QM_ASSIGN may be optimized out when op1 is null).
    (Arnaud, Dmitry)
  . Fixed GH-13712 (Segmentation fault for enabled observers when calling trait
    method of internal trait when opcache is loaded). (Bob)

- Random:
  . Fixed bug GH-13544 (Pre-PHP 8.2 compatibility for mt_srand with unknown
    modes). (timwolla)
  . Fixed bug GH-13690 (Global Mt19937 is not properly reset in-between
    requests when MT_RAND_PHP is used). (timwolla)

- Session:
  . Fixed bug GH-13680 (Segfault with session_decode and compilation error).
    (nielsdos)

- SPL:
  . Fixed bug GH-13685 (Unexpected null pointer in zend_string.h). (nielsdos)

- Standard:
  . Fixed bug GH-11808 (Live filesystem modified by tests). (nielsdos)
  . Fixed GH-13402 (Added validation of `\n` in $additional_headers of mail()).
    (SakiTakamachi)
  . Fixed bug GH-13203 (file_put_contents fail on strings over 4GB on Windows).
    (divinity76)
  . Fixed bug GHSA-pc52-254m-w9w7 (Command injection via array-ish $command
    parameter of proc_open). (CVE-2024-1874) (Jakub Zelenka)
  . Fixed bug GHSA-wpj3-hf5j-x4v4 (__Host-/__Secure- cookie bypass due to
    partial CVE-2022-31629 fix). (CVE-2024-2756) (nielsdos)
  . Fixed bug GHSA-h746-cjrr-wfmr (password_verify can erroneously return true,
    opening ATO risk). (CVE-2024-3096) (Jakub Zelenka)
  . Fixed bug GHSA-fjp9-9hwx-59fq (mb_encode_mimeheader runs endlessly for some
    inputs). (CVE-2024-2757) (Alex Dowad)

14 Mar 2024, PHP 8.3.4

- Core:
  . Fix ZTS persistent resource crashes on shutdown. (nielsdos)

- Curl:
  . Fix failing tests due to string changes in libcurl 8.6.0. (Ayesh)

- DOM:
  . Fix unlikely memory leak in case of namespace removal with extremely deep
    trees. (nielsdos)
  . Fix reference access in dimensions for DOMNodeList and DOMNodeMap.
    (nielsdos)

- Fileinfo:
  . Fixed bug GH-13344 (finfo::buffer(): Failed identify data 0:(null),
    backport). (nielsdos)

- FPM:
  . Fixed bug #75712 (getenv in php-fpm should not read $_ENV, $_SERVER).
    (Jakub Zelenka)

- GD:
  . Fixed bug GH-12019 (detection of image formats in system gd library).
    (Michael Orlitzky)

- MySQLnd:
  . Fixed bug GH-11950 ([mysqlnd] Fixed not to set CR_MALFORMED_PACKET to error
    if CR_SERVER_GONE_ERROR is already set). (Saki Takamachi)

- PDO:
  . Fix various PDORow bugs. (Girgias)

- PGSQL:
  . Fixed bug GH-13354 (pg_execute/pg_send_query_params/pg_send_execute
    with null value passed by reference). (George Barbarosie)

- SPL:
  . Fixed bug GH-13531 (Unable to resize SplfixedArray after being unserialized
    in PHP 8.2.15). (nielsdos)

- Standard:
  . Fixed bug GH-13279 (Instable array during in-place modification in uksort).
    (ilutov)
  . Fixed array key as hash to string (case insensitive) comparison typo
    for the second operand buffer size (albeit unused for now). (A. Slepykh)

- XML:
  . Fixed bug GH-13517 (Multiple test failures when building with
    --with-expat). (nielsdos)

15 Feb 2024, PHP 8.3.3

- Core:
  . Fixed timer leak in zend-max-execution-timers builds. (withinboredom)
  . Fixed bug GH-12349 (linking failure on ARM with mold). (Jan Palus)
  . Fixed bug GH-13097 (Anonymous class reference in trigger_error / thrown
    Exception). (nielsdos)
  . Fixed bug GH-13177 (PHP 8.3.2: final private constructor not allowed
    when used in trait). (nielsdos)
  . Fixed bug GH-13215 (GCC 14 build failure). (Remi)

- Curl:
  . Fix missing error check in curl_multi_init(). (divinity76)

- FPM:
  . Fixed bug GH-12996 (Incorrect SCRIPT_NAME with Apache ProxyPassMatch when
    plus in path). (Jakub Zelenka)

- GD:
  . Fixed bug GH-10344 (imagettfbbox(): Could not find/open font UNC path).
    (nielsdos)
  . Fixed bug GH-10614 (imagerotate will turn the picture all black, when
    rotated 90). (nielsdos)

- LibXML:
  . Fix crashes with entity references and predefined entities. (nielsdos)

- MySQLnd:
  . Fixed bug GH-12107 (When running a stored procedure (that returns a result
    set) twice, PHP crashes). (nielsdos)

- Opcache:
  . Fixed bug GH-13145 (strtok() is not comptime). (ilutov)
  . Fixed type inference of range(). (ilutov)
  . Fixed bug GH-13232 (Segmentation fault will be reported when JIT is off but
    JIT_debug is still on). (nielsdos)

- OpenSSL:
  . Fixed LibreSSL undefined reference when OPENSSL_NO_ENGINE not set.
   (David Carlier).

- PDO_Firebird:
  . Fix GH-13119 (Changed to convert float and double values into strings using
    `H` format). (SakiTakamachi)

- Phar:
  . Fixed bug #71465 (PHAR doesn't know about litespeed). (nielsdos)
  . Fixed bug GH-13037 (PharData incorrectly extracts zip file). (nielsdos)

- Random:
  . Fixed bug GH-13138 (Randomizer::pickArrayKeys() does not detect broken
    engines). (timwolla)

- Session:
  . Fixed bug GH-12504 (Corrupted session written when there's a fatal error
    in autoloader). (nielsdos)

- Standard:
  . Fixed bug GH-13094 (range(9.9, '0') causes segmentation fault). (nielsdos)

- Streams:
  . Fixed bug GH-13071 (Copying large files using mmap-able source streams may
    exhaust available memory and fail). (nielsdos)

18 Jan 2024, PHP 8.3.2

- Core:
  . Fixed bug GH-12953 (false positive SSA integrity verification failed when
    loading composer classmaps with more than 11k elements). (nielsdos)
  . Fixed bug GH-12999 (zend_strnlen build when strnlen is unsupported).
    (rainerjung)
  . Fixed bug GH-12966 (missing cross-compiling 3rd argument so Autoconf
    doesn't emit warnings). (Peter Kokot)
  . Fixed bug GH-12854 (8.3 - as final trait-used method does not correctly
    report visibility in Reflection). (nielsdos)

- Cli:
  . Fix incorrect timeout in built-in web server when using router script and
    max_input_time. (ilutov)

- DOM:
  . Fixed bug GH-12870 (Creating an xmlns attribute results in a DOMException).
    (nielsdos)
  . Fix crash when toggleAttribute() is used without a document. (nielsdos)
  . Fix crash in adoptNode with attribute references. (nielsdos)
  . Fixed bug GH-13012 (DOMNode::isEqualNode() is incorrect when attribute
    order is different). (nielsdos)

- FFI:
  . Fixed bug GH-9698 (stream_wrapper_register crashes with FFI\CData).
    (Jakub Zelenka)
  . Fixed bug GH-12905 (FFI::new interacts badly with observers). (nielsdos)

- GD:
  . Fixed GH-13082 undefined behavior with GdFont instances handling with
    imageload* and imagechar*. (David Carlier)

- Intl:
  . Fixed GH-12943 (IntlDateFormatter::__construct accepts 'C' as valid locale).
    (David Carlier)

- Hash:
  . Fixed bug GH-12936 (hash() function hangs endlessly if using sha512 on
    strings >= 4GiB). (nielsdos)

- MBString:
  . When operating on a string with invalid encoding, mb_substr (as well
    as mb_strstr and its variants) defines character indices in the same
    way as other mbstring functions such as mb_strpos. (Alex Dowad)

- ODBC:
  . Fix crash on Apache shutdown with persistent connections. (nielsdos)

- Opcache:
  . Fixed oss-fuzz #64727 (JIT undefined array key warning may overwrite DIM
    with NULL when DIM is the same var as result). (ilutov)
  . Added workaround for SELinux mprotect execheap issue.
    See https://bugzilla.kernel.org/show_bug.cgi?id=218258. (ilutov)

- OpenSSL:
  . Fixed bug GH-12987 (openssl_csr_sign might leak new cert on error).
    (Jakub Zelenka)

- PDO:
  . Fix GH-12969 (Fixed PDO::getAttribute() to get PDO::ATTR_STRINGIFY_FETCHES).
    (SakiTakamachi)

- PDO_ODBC:
  . Fixed bug GH-12767 (Unable to turn on autocommit mode with setAttribute()).
    (SakiTakamachi)

- PGSQL:
  . Fixed auto_reset_persistent handling and allow_persistent type. (David Carlier)
  . Fixed bug GH-12974 (Apache crashes on shutdown when using pg_pconnect()).
    (nielsdos)

- Phar:
  . Fixed bug #77432 (Segmentation fault on including phar file). (nielsdos)

- PHPDBG:
  . Fixed bug GH-12962 (Double free of init_file in phpdbg_prompt.c). (nielsdos)

- SimpleXML:
  . Fix getting the address of an uninitialized property of a SimpleXMLElement
    resulting in a crash. (nielsdos)
  . Fixed bug GH-12929 (SimpleXMLElement with stream_wrapper_register can
    segfault). (nielsdos)

- Tidy:
  . Fixed bug GH-12980 (tidynode.props.attribute is missing
    "Boolean Attributes" and empty attributes). (nielsdos)

21 Dec 2023, PHP 8.3.1

- Core:
  . Fixed bug GH-12758 / GH-12768 (Invalid opline in OOM handlers within
    ZEND_FUNC_GET_ARGS and ZEND_BIND_STATIC). (Florian Engelhardt)
  . Fix various missing NULL checks. (nielsdos, dstogov)
  . Fixed bug GH-12835 (Leak of call->extra_named_params on internal __call).
    (ilutov)
  . Fixed bug GH-12826 (Weird pointers issue in nested loops). (nielsdos)

- FPM:
  . Fixed bug GH-12705 (Segmentation fault in fpm_status_export_to_zval).
    (Patrick Prasse)

- FTP:
  . Fixed bug GH-9348 (FTP & SSL session reuse). (nielsdos)

- LibXML:
  . Fixed test failures for libxml2 2.12.0. (nielsdos)

- MySQLnd:
  . Avoid using uninitialised struct. (mikhainin)
  . Fixed bug GH-12791 (Possible dereference of NULL in MySQLnd debug code).
    (nielsdos)

- Opcache:
  . Fixed JIT bug (Function JIT emits "Uninitialized string offset" warning
    at the same time as invalid offset Error). (Girgias)
  . Fixed JIT bug (JIT emits "Attempt to assign property of non-object"
    warning at the same time as Error is being thrown). (Girgias)

- PDO PGSQL:
  . Fixed the default value of $fetchMode in PDO::pgsqlGetNotify() (kocsismate)

- SOAP:
  . Fixed bug GH-12838 ([SOAP] Temporary WSDL cache files not being deleted).
    (nielsdos)

- Standard
  . Fixed GH-12745 (http_build_query() default null argument for $arg_separator
    is implicitly coerced to string). (Girgias)

23 Nov 2023, PHP 8.3.0

- Bcmath
  . Fixed GH-11761 (removing trailing zeros from numbers) (jorgsowa)

- CLI:
  . Added pdeathsig to builtin server to terminate workers when the master
    process is killed. (ilutov)
  . Fixed bug GH-11104 (STDIN/STDOUT/STDERR is not available for CLI without
    a script). (nielsdos)
  . Implement GH-10024 (support linting multiple files at once using php -l).
    (nielsdos)

- Core:
  . Fix GH-11388 (Allow "final" modifier when importing a method from a trait).
    (nielsdos)
  . Fixed bug GH-11406 (segfault with unpacking and magic method closure).
    (nielsdos)
  . Fixed bug GH-9388 (Improve unset property and __get type incompatibility
    error message). (ilutov)
  . SA_ONSTACK is now set for signal handlers to be friendlier to other
    in-process code such as Go's cgo. (Kévin Dunglas)
  . SA_ONSTACK is now set when signals are disabled. (Kévin Dunglas)
  . Fix GH-9649: Signal handlers now do a no-op instead of crashing when
    executed on threads not managed by TSRM. (Kévin Dunglas)
  . Added shadow stack support for fibers. (Chen Hu)
  . Fix bug GH-9965 (Fix accidental caching of default arguments with side
    effects). (ilutov)
  . Implement GH-10217 (Use strlen() for determining the class_name length).
    (Dennis Buteyn)
  . Fix bug GH-8821 (Improve line numbers for errors in constant expressions).
    (ilutov)
  . Fix bug GH-10083 (Allow comments between & and parameter). (ilutov)
  . Zend Max Execution Timers is now enabled by default for ZTS builds on
    Linux. (Kévin Dunglas)
  . Fix bug GH-10469 (Disallow .. in open_basedir paths set at runtime).
    (ilutov)
  . Fix bug GH-10168, GH-10582 (Various segfaults with destructors and VM return
    values). (dstogov, nielsdos, ilutov)
  . Fix bug GH-10935 (Use of trait doesn't redeclare static property if class
    has inherited it from its parent). (ilutov)
  . Fix bug GH-11154 (Negative indices on empty array don't affect next chosen
    index). (ColinHDev)
  . Fix bug GH-8846 (Implement delayed early binding for classes without
    parents). (ilutov)
  . Fix bug #79836 (Segfault in concat_function). (nielsdos)
  . Fix bug #81705 (type confusion/UAF on set_error_handler with concat
    operation). (nielsdos)
  . Fix GH-11348 (Closure created from magic method does not accept named
    arguments). (nielsdos)
  . Fix GH-11388 (Allow "final" modifier when importing a method from a trait).
    (nielsdos)
  . Fixed bug GH-11406 (segfault with unpacking and magic method closure).
    (nielsdos)
  . Fixed bug GH-11507 (String concatenation performance regression in 8.3).
    (nielsdos)
  . Fixed GH-11488 (Missing "Optional parameter before required" deprecation on
    union null type). (ilutov)
  . Implement the #[\Override] attribute RFC. (timwolla)
  . Fixed bug GH-11601 (Incorrect handling of unwind and graceful exit
    exceptions). (ilutov)
  . Added zend_call_stack_get implementation for OpenBSD. (David Carlier)
  . Add stack limit check in zend_eval_const_expr(). (Arnaud)
  . Expose time spent collecting cycles in gc_status(). (Arnaud)
  . Remove WeakMap entries whose key is only reachable through the entry value.
    (Arnaud)
  . Resolve open_basedir paths on INI update. (ilutov)
  . Fixed oss-fuzz #60741 (Leak in open_basedir). (ilutov)
  . Fixed segfault during freeing of some incompletely initialized objects due
    to OOM error (PDO, SPL, XSL). (ilutov)
  . Introduced Zend guard recursion protection to fix __debugInfo issue.
    (Jakub Zelenka)
  . Fixed oss-fuzz #61712 (assertion failure with error handler during binary
    op). (nielsdos)
  . Fixed GH-11847 (DTrace enabled build is broken). (Filip Zrůst)
  . Fixed OSS Fuzz #61865 (Undef variable in ++/-- for declared property
    that is unset in error handler). (Girgias)
  . Fixed warning emitted when checking if a user stream is castable. (Girgias)
  . Fixed bug GH-12123 (Compile error on MacOS with C++ extension when using
    ZEND_BEGIN_ARG_WITH_RETURN_TYPE_INFO_EX). (kocsismate)
  . Fixed bug GH-12189 (#[Override] attribute in trait does not check for
    parent class implementations). (timwolla)
  . Fixed OSS Fuzz #62294 (Unsetting variable after ++/-- on string variable
    warning). (Girgias)
  . Fixed buffer underflow when compiling memoized expression. (ilutov)
  . Fixed oss-fuzz #63802 (OP1 leak in error path of post inc/dec). (ilutov)

- Curl:
  . Added Curl options and constants up to (including) version 7.87.
    (nielsdos, adoy)

- Date:
  . Implement More Appropriate Date/Time Exceptions RFC. (Derick)

- DOM:
  . Fix bug GH-8388 (DOMAttr unescapes character reference). (Tim Starling)
  . Fix bug GH-11308 (getElementsByTagName() is O(N^2)). (nielsdos)
  . Fix #79700 (wrong use of libxml oldNs leads to performance problem).
    (nielsdos)
  . Fix #77894 (DOMNode::C14N() very slow on generated DOMDocuments even after
    normalisation). (nielsdos)
  . Revert changes to DOMAttr::$value and DOMAttr::$nodeValue expansion.
    (nielsdos)
  . Fixed bug GH-11500 (Namespace reuse in createElementNS() generates wrong
    output). (nielsdos)
  . Implemented DOMDocument::adoptNode(). Previously this always threw a
    "not yet implemented" exception. (nielsdos)
  . Fixed bug GH-9628 (Implicitly removing nodes from \DOMDocument breaks
    existing references). (nielsdos)
  . Added DOMNode::contains() and DOMNameSpaceNode::contains(). (nielsdos)
  . Added DOMElement::getAttributeNames(). (nielsdos)
  . Added DOMNode::getRootNode(). (nielsdos)
  . Added DOMElement::className and DOMElement::id. (nielsdos)
  . Added DOMParentNode::replaceChildren(). (nielsdos)
  . Added DOMNode::isConnected and DOMNameSpaceNode::isConnected. (nielsdos)
  . Added DOMNode::parentElement and DOMNameSpaceNode::parentElement.
    (nielsdos)
  . Added DOMNode::isEqualNode(). (nielsdos)
  . Added DOMElement::insertAdjacentElement() and
    DOMElement::insertAdjacentText(). (nielsdos)
  . Added DOMElement::toggleAttribute(). (nielsdos)
  . Fixed bug GH-11792 (LIBXML_NOXMLDECL is not implemented or broken).
    (nielsdos)
  . adoptNode now respects the strict error checking property. (nielsdos)
  . Align DOMChildNode parent checks with spec. (nielsdos)
  . Fixed bug #80927 (Removing documentElement after creating attribute node:
    possible use-after-free). (nielsdos)
  . Fix various namespace prefix conflict resolution bugs. (nielsdos)
  . Fix calling createAttributeNS() without prefix causing the default
    namespace of the element to change. (nielsdos)
  . Fixed GH-11952 (Confusing warning when blocking entity loading via
    libxml_set_external_entity_loader). (nielsdos)
  . Fix broken cache invalidation with deallocated and reallocated document
    node. (nielsdos)
  . Fix compile error when php_libxml.h header is included in C++.
    (Remi, nielsdos)
  . Fixed bug #47531 (No way of removing redundant xmlns: declarations).
    (nielsdos)

- Exif:
  . Removed unneeded codepaths in exif_process_TIFF_in_JPEG(). (nielsdos)

- FFI:
  . Implement GH-11934 (Allow to pass CData into struct and/or union fields).
    (nielsdos, KapitanOczywisty)

- Fileinfo:
  . Upgrade bundled libmagic to 5.43. (Anatol)
  . Fix GH-11408 (Unable to build PHP 8.3.0 alpha 1 / fileinfo extension).
    (nielsdos)

- FPM:
  . The status.listen shared pool now uses the same php_values (including
    expose_php) and php_admin_value as the pool it is shared with. (dwxh)
  . Added warning to log when fpm socket was not registered on the expected
    path. (Joshua Behrens, Jakub Zelenka)
  . Fixed bug #76067 (system() function call leaks php-fpm listening sockets).
    (Mikhail Galanin, Jakub Zelenka)
  . Fixed GH-12077 (PHP 8.3.0RC1 borked socket-close-on-exec.phpt).
    (Jakub Zelenka)

- GD:
  . Removed imagerotate "ignore_transparent" argument since it has no effect.
    (David Carlier)

- Intl:
  . Added pattern format error infos for numfmt_set_pattern. (David Carlier)
  . Added MIXED_NUMBERS and HIDDEN_OVERLAY constants for
    the Spoofchecker's class. (David Carlier)
  . Updated datefmt_set_timezone/IntlDateformatter::setTimezone returns type.
    (David Carlier).
  . Updated IntlBreakInterator::setText return type. (David Carlier)
  . Updated IntlChar::enumCharNames return type. (David Carlier)
  . Removed the BC break on IntlDateFormatter::construct which threw an
    exception with an invalid locale. (David Carlier)

- JSON:
  . Added json_validate(). (Juan Morales)

- LDAP:
  . Deprecate calling ldap_connect() with separate hostname and port.
    (heiglandreas)

- LibXML:
  . Fix compile error with -Werror=incompatible-function-pointer-types and
    old libxml2. (nielsdos)

- MBString:
  . mb_detect_encoding is better able to identify the correct encoding for
    Turkish text. (Alex Dowad)
  . mb_detect_encoding's "non-strict" mode now behaves as described in the
    documentation. Previously, it would return false if the same byte
    (for example, the first byte) of the input string was invalid in all
    candidate encodings. More generally, it would eliminate candidate
    encodings from consideration when an invalid byte was seen, and if the
    same input byte eliminated all remaining encodings still under
    consideration, it would return false. On the other hand, if all candidate
    encodings but one were eliminated from consideration, it would return the
    last remaining one without regard for how many encoding errors might be
    encountered later in the string. This is different from the behavior
    described in the documentation, which says: "If strict is set to false,
    the closest matching encoding will be returned." (Alex Dowad)
  . mb_strtolower, mb_strtotitle, and mb_convert_case implement conditional
    casing rules for the Greek letter sigma. For mb_convert_case, conditional
    casing only applies to MB_CASE_LOWER and MB_CASE_TITLE modes, not to
    MB_CASE_LOWER_SIMPLE and MB_CASE_TITLE_SIMPLE. (Alex Dowad)
  . mb_detect_encoding is better able to identify UTF-8 and UTF-16 strings
    with a byte-order mark. (Alex Dowad)
  . mb_decode_mimeheader interprets underscores in QPrint-encoded MIME
    encoded words as required by RFC 2047; they are converted to spaces.
    Underscores must be encoded as "=5F" in such MIME encoded words.
    (Alex Dowad)
  . mb_encode_mimeheader no longer drops NUL (zero) bytes when
    QPrint-encoding the input string. This previously caused strings in
    certain text encodings, especially UTF-16 and UTF-32, to be
    corrupted by mb_encode_mimeheader. (Alex Dowad)
  . Implement mb_str_pad() RFC. (nielsdos)
  . Fixed bug GH-11514 (PHP 8.3 build fails with --enable-mbstring enabled).
    (nielsdos)
  . Fix use-after-free of mb_list_encodings() return value. (ilutov)
  . Fixed bug GH-11992 (utf_encodings.phpt fails on Windows 32-bit). (nielsdos)

- mysqli:
  . mysqli_fetch_object raises a ValueError instead of an Exception.
    (David Carlier)

- Opcache:
  . Added start, restart and force restart time to opcache's
    phpinfo section. (Mikhail Galanin)
  . Fix GH-9139: Allow FFI in opcache.preload when opcache.preload_user=root.
    (Arnaud, Kapitan Oczywisty)
  . Made opcache.preload_user always optional in the cli and phpdbg SAPIs.
    (Arnaud)
  . Allows W/X bits on page creation on FreeBSD despite system settings.
    (David Carlier)
  . Added memfd api usage, on Linux, for zend_shared_alloc_create_lock()
    to create an abstract anonymous file for the opcache's lock. (Max Kellermann)
  . Avoid resetting JIT counter handlers from multiple processes/threads.
    (ilutov)
  . Fixed COPY_TMP type inference for references. (ilutov)

- OpenSSL:
  . Added OPENSSL_CMS_OLDMIMETYPE and PKCS7_NOOLDMIMETYPE contants to switch
    between mime content types. (Daniel Kesselberg)
  . Fixed GH-11054: Reset OpenSSL errors when using a PEM public key.
    (Florian Moser)
  . Added support for additional EC parameters in openssl_pkey_new. (Eno-CN)

- PCNTL:
  . SA_ONSTACK is now set for pcntl_signal. (Kévin Dunglas)
  . Added SIGINFO constant. (David Carlier)

- PCRE:
  . Update bundled libpcre2 to 10.42. (nielsdos)

- PGSQL:
  . pg_fetch_object raises a ValueError instead of an Exception.
    (David Carlier)
  . pg_cancel use thread safe PQcancel api instead. (David Carlier)
  . pg_trace new PGSQL_TRACE_SUPPRESS_TIMESTAMPS/PGSQL_TRACE_REGRESS_MODE
    contants support. (David Carlier)
  . pg_set_error_verbosity adding PGSQL_ERRORS_STATE constant. (David Carlier)
  . pg_convert/pg_insert E_WARNING on type errors had been converted to
    ValueError/TypeError exceptions. (David Carlier)
  . Added pg_set_error_context_visibility to set the context's visibility
    within the error messages. (David Carlier)

- Phar:
  . Fix memory leak in phar_rename_archive(). (stkeke)

- POSIX:
  . Added posix_sysconf. (David Carlier)
  . Added posix_pathconf. (David Carlier)
  . Added posix_fpathconf. (David Carlier)
  . Fixed zend_parse_arg_long's bool pointer argument assignment. (Cristian Rodriguez)
  . Added posix_eaccess. (David Carlier)

- Random:
  . Added Randomizer::getBytesFromString(). (Joshua Rüsweg)
  . Added Randomizer::nextFloat(), ::getFloat(), and IntervalBoundary. (timwolla)
  . Enable getrandom() for NetBSD (from 10.x). (David Carlier)
  . Deprecate MT_RAND_PHP. (timwolla)
  . Fix Randomizer::getFloat() returning incorrect results under
    certain circumstances. (timwolla)

- Reflection:
  . Fix GH-9470 (ReflectionMethod constructor should not find private parent
    method). (ilutov)
  . Fix GH-10259 (ReflectionClass::getStaticProperties doesn't need null return
    type). (kocsismate)

- SAPI:
  . Fixed GH-11141 (Could not open input file: should be sent to stderr).
    (nielsdos)

- Session:
  . Fixed bug GH-11529 (Crash after dealing with an Apache request). (nielsdos)

- SimpleXML:
  . Fixed bug GH-12192 (SimpleXML infinite loop when getName() is called
    within foreach). (nielsdos)
  . Fixed bug GH-12208 (SimpleXML infinite loop when a cast is used inside a
    foreach). (nielsdos)
  . Fixed bug #55098 (SimpleXML iteration produces infinite loop). (nielsdos)

- Sockets:
  . Added SO_ATTACH_REUSEPORT_CBPF socket option, to give tighter control
    over socket binding for a cpu core. (David Carlier)
  . Added SKF_AD_QUEUE for cbpf filters. (David Carlier)
  . Added socket_atmark if send/recv needs using MSG_OOB. (David Carlier)
  . Added TCP_QUICKACK constant, to give tigher control over
    ACK delays. (David Carlier)
  . Added DONTFRAGMENT support for path MTU discovery purpose. (David Carlier)
  . Added AF_DIVERT for raw socket for divert ports. (David Carlier)
  . Added SOL_UPDLITE, UDPLITE_RECV_CSCOV and UDPLITE_SEND_CSCOV for updlite
    protocol support. (David Carlier)
  . Added SO_RERROR, SO_ZEROIZE and SO_SPLICE netbsd and openbsd constants.
    (David Carlier)
  . Added TCP_REPAIR for quietly close a connection. (David Carlier)
  . Added SO_REUSEPORT_LB freebsd constant. (David Carlier)
  . Added IP_BIND_ADDRESS_NO_PORT. (David Carlier)

- SPL:
  . Fixed GH-11573 (RecursiveDirectoryIterator::hasChildren is slow).
    (nielsdos)

- Standard:
  . E_NOTICEs emitted by unserialize() have been promoted to E_WARNING. (timwolla)
  . unserialize() now emits a new E_WARNING if the input contains unconsumed
    bytes. (timwolla)
  . Make array_pad's $length warning less confusing. (nielsdos)
  . E_WARNING emitted by strtok in the caase both arguments are not provided when
    starting tokenisation. (David Carlier)
  . password_hash() will now chain the original RandomException to the ValueError
    on salt generation failure. (timwolla)
  . Fix GH-10239 (proc_close after proc_get_status always returns -1). (nielsdos)
  . Improve the warning message for unpack() in case not enough values were
    provided. (nielsdos)
  . Fix GH-11010 (parse_ini_string() now preserves formatting of unquoted
    strings starting with numbers when the INI_SCANNER_TYPED flag is
    specified). (ilutov)
  . Fix GH-10742 (http_response_code emits no error when headers were already
    sent). (NattyNarwhal)
  . Added support for rounding negative places in number_format().
    (Marc Bennewitz)
  . Prevent precision loss on formatting decimal integers in number_format().
    (Marc Bennewitz)
  . Added usage of posix_spawn for proc_open when supported by OS.
    (Cristian Rodriguez)
  . Added $before_needle argument to strrchr(). (HypeMC)
  . Fixed GH-11982 (str_getcsv returns null byte for unterminated enclosure).
    (Jakub Zelenka)
  . Fixed str_decrement() on "1". (ilutov)

- Streams:
  . Fixed bug #51056: blocking fread() will block even if data is available.
    (Jakub Zelenka)
  . Added storing of the original path used to open xport stream.
    (Luc Vieillescazes)
  . Implement GH-8641 (STREAM_NOTIFY_COMPLETED over HTTP never emitted).
    (nielsdos, Jakub Zelenka)
  . Fix bug GH-10406 (fgets on a redis socket connection fails on PHP 8.3).
    (Jakub Zelenka)
  . Implemented GH-11242 (_php_stream_copy_to_mem: Allow specifying a maximum
    length without allocating a buffer of that size). (Jakub Zelenka)
  . Fixed bug #52335 (fseek() on memory stream behavior different than file).
    (Jakub Zelenka)
  . Fixed bug #76857 (Can read "non-existant" files). (Jakub Zelenka)

- XSLTProcessor:
  . Fixed bug #69168 (DomNode::getNodePath() returns invalid path). (nielsdos)

- ZIP:
  . zip extension version 1.22.0 for libzip 1.10.0. (Remi)
  . add new error macros (ER_DATA_LENGTH and ER_NOT_ALLOWED). (Remi)
  . add new archive global flags (ER_AFL_*). (Remi)
  . add ZipArchive::setArchiveFlag and ZipArchive::getArchiveFlag methods.
    (Remi)
