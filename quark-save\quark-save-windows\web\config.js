// 配置页面JavaScript
class ConfigApp {
    constructor() {
        this.cookieInput = document.getElementById('cookieInput');
        this.phpVersionSpan = document.getElementById('phpVersion');
        this.cookieStatusSpan = document.getElementById('cookieStatus');
        this.serviceStatusSpan = document.getElementById('serviceStatus');
        this.lastUpdateSpan = document.getElementById('lastUpdate');
        this.init();
    }

    async init() {
        await this.loadSystemInfo();
        await this.loadCurrentCookie();
        this.setupEventListeners();
        this.updateLastUpdateTime();
    }

    async loadSystemInfo() {
        try {
            const response = await fetch('/api/status');
            const data = await response.json();
            
            if (data.status === 'ok') {
                this.phpVersionSpan.textContent = data.phpVersion;
                this.serviceStatusSpan.className = 'badge bg-success';
                this.serviceStatusSpan.textContent = '运行中';
                
                if (data.hasCookie) {
                    this.cookieStatusSpan.className = 'badge bg-success';
                    this.cookieStatusSpan.textContent = '已配置';
                } else {
                    this.cookieStatusSpan.className = 'badge bg-warning';
                    this.cookieStatusSpan.textContent = '未配置';
                }
            }
        } catch (error) {
            this.serviceStatusSpan.className = 'badge bg-danger';
            this.serviceStatusSpan.textContent = '离线';
            console.error('Failed to load system info:', error);
        }
    }

    async loadCurrentCookie() {
        try {
            const response = await fetch('/api/config');
            const data = await response.json();
            
            if (data.status === 'success' && data.cookie) {
                this.cookieInput.value = data.cookie;
            }
        } catch (error) {
            console.error('Failed to load cookie:', error);
        }
    }

    async saveCookie() {
        const cookie = this.cookieInput.value.trim();
        
        if (!cookie) {
            this.showToast('请输入Cookie内容', 'error');
            return;
        }

        try {
            const response = await fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ cookie: cookie })
            });

            const data = await response.json();
            
            if (data.status === 'success') {
                this.showToast('Cookie保存成功', 'success');
                await this.loadSystemInfo(); // 重新加载状态
            } else {
                this.showToast(data.message || 'Cookie保存失败', 'error');
            }
        } catch (error) {
            this.showToast('网络错误: ' + error.message, 'error');
        }
    }

    setupEventListeners() {
        // 设置全局函数
        window.loadCurrentCookie = this.loadCurrentCookie.bind(this);
        window.saveCookie = this.saveCookie.bind(this);
        window.clearLogs = this.clearLogs.bind(this);
        window.exportConfig = this.exportConfig.bind(this);
        window.importConfig = this.importConfig.bind(this);
        window.resetConfig = this.resetConfig.bind(this);
        window.testApiConnection = this.testApiConnection.bind(this);
        window.createScheduledTask = this.createScheduledTask.bind(this);
        window.refreshLogStats = this.refreshLogStats.bind(this);
        window.cleanOldLogs = this.cleanOldLogs.bind(this);
        window.openLogFolder = this.openLogFolder.bind(this);

        // Cookie输入框自动保存
        this.cookieInput.addEventListener('input', () => {
            // 可以添加自动保存逻辑
        });

        // 设置项变化监听
        const settings = ['requestDelay', 'maxRetries', 'autoRefresh', 'enableNotifications'];
        settings.forEach(setting => {
            const element = document.getElementById(setting);
            if (element) {
                element.addEventListener('change', () => {
                    this.saveSettings();
                });
            }
        });
    }

    saveSettings() {
        const settings = {
            requestDelay: document.getElementById('requestDelay').value,
            maxRetries: document.getElementById('maxRetries').value,
            autoRefresh: document.getElementById('autoRefresh').checked,
            enableNotifications: document.getElementById('enableNotifications').checked
        };

        localStorage.setItem('quarkAppSettings', JSON.stringify(settings));
        this.showToast('设置已保存', 'success');
    }

    loadSettings() {
        const saved = localStorage.getItem('quarkAppSettings');
        if (saved) {
            const settings = JSON.parse(saved);
            
            document.getElementById('requestDelay').value = settings.requestDelay || 2;
            document.getElementById('maxRetries').value = settings.maxRetries || 3;
            document.getElementById('autoRefresh').checked = settings.autoRefresh !== false;
            document.getElementById('enableNotifications').checked = settings.enableNotifications || false;
        }
    }

    clearLogs() {
        if (confirm('确定要清空所有日志吗？')) {
            // 这里可以调用API清空日志
            this.showToast('日志已清空', 'success');
        }
    }

    exportConfig() {
        const config = {
            cookie: this.cookieInput.value,
            settings: JSON.parse(localStorage.getItem('quarkAppSettings') || '{}'),
            timestamp: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `quark-config-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        this.showToast('配置已导出', 'success');
    }

    importConfig() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const config = JSON.parse(e.target.result);
                        
                        if (config.cookie) {
                            this.cookieInput.value = config.cookie;
                        }
                        
                        if (config.settings) {
                            localStorage.setItem('quarkAppSettings', JSON.stringify(config.settings));
                            this.loadSettings();
                        }
                        
                        this.showToast('配置已导入', 'success');
                    } catch (error) {
                        this.showToast('配置文件格式错误', 'error');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    resetConfig() {
        if (confirm('确定要重置所有配置吗？这将清空Cookie和所有设置。')) {
            this.cookieInput.value = '';
            localStorage.removeItem('quarkAppSettings');
            this.loadSettings();
            this.showToast('配置已重置', 'success');
        }
    }

    showToast(message, type = 'info') {
        const toast = document.getElementById('messageToast');
        const toastBody = document.getElementById('toastMessage');
        const toastHeader = toast.querySelector('.toast-header i');
        
        // 设置图标和颜色
        const icons = {
            'success': 'bi-check-circle text-success',
            'error': 'bi-exclamation-circle text-danger',
            'warning': 'bi-exclamation-triangle text-warning',
            'info': 'bi-info-circle text-primary'
        };
        
        toastHeader.className = `${icons[type] || icons.info} me-2`;
        toastBody.textContent = message;
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }

    updateLastUpdateTime() {
        this.lastUpdateSpan.textContent = new Date().toLocaleString();

        // 每分钟更新一次时间
        setInterval(() => {
            this.lastUpdateSpan.textContent = new Date().toLocaleString();
        }, 60000);
    }

    async testApiConnection() {
        this.showToast('正在测试API连接...', 'info');

        try {
            const response = await fetch('/api/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'api_today',
                    params: {}
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.showToast('API连接测试成功', 'success');
            } else {
                this.showToast('API连接测试失败: ' + (data.output || '未知错误'), 'error');
            }
        } catch (error) {
            this.showToast('API连接测试失败: ' + error.message, 'error');
        }
    }

    createScheduledTask() {
        const confirmed = confirm(
            '这将创建一个每日定时任务，在每天上午9点自动更新短剧资源。\n\n' +
            '注意：此操作需要管理员权限。\n\n' +
            '是否继续？'
        );

        if (!confirmed) {
            return;
        }

        this.showToast('正在创建定时任务...', 'info');

        // 这里可以调用后端API来创建定时任务
        // 或者提示用户手动运行批处理文件
        setTimeout(() => {
            this.showToast(
                '请以管理员身份运行"创建定时任务.bat"文件来创建定时任务',
                'warning'
            );
        }, 1000);
    }

    async refreshLogStats() {
        this.showToast('正在获取日志统计...', 'info');

        try {
            // 这里可以调用后端API获取日志统计
            // 暂时显示模拟数据
            const logStatsDiv = document.getElementById('logStats');
            logStatsDiv.innerHTML = `
                <div class="row">
                    <div class="col-6"><strong>API日志:</strong> 3 文件</div>
                    <div class="col-6"><strong>系统日志:</strong> 1 文件</div>
                    <div class="col-6"><strong>分享日志:</strong> 0 文件</div>
                    <div class="col-6"><strong>同步日志:</strong> 0 文件</div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">最后更新: ${new Date().toLocaleString()}</small>
                </div>
            `;

            this.showToast('日志统计已更新', 'success');
        } catch (error) {
            this.showToast('获取日志统计失败: ' + error.message, 'error');
        }
    }

    cleanOldLogs() {
        const days = document.getElementById('logRetentionDays').value;

        const confirmed = confirm(
            `确定要清理 ${days} 天前的日志文件吗？\n\n` +
            '此操作不可撤销，建议先备份重要日志。'
        );

        if (!confirmed) {
            return;
        }

        this.showToast(`正在清理 ${days} 天前的日志...`, 'info');

        // 这里可以调用后端API清理日志
        setTimeout(() => {
            this.showToast('日志清理完成', 'success');
            this.refreshLogStats(); // 刷新统计
        }, 2000);
    }

    openLogFolder() {
        this.showToast('日志目录位于程序根目录下的 logs 文件夹', 'info');

        // 在实际环境中，可以尝试打开文件夹
        // 但在Web环境中有限制，只能提示用户手动打开
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.configApp = new ConfigApp();
});
