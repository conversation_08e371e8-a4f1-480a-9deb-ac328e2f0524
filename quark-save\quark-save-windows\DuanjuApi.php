<?php

/**
 * 短剧API接口类
 * 用于调用短剧查询API获取最新短剧资源
 */
class DuanjuApi
{
    private $baseUrl = 'https://www.duanju.click/api/short/quark';
    private $timeout = 30;
    private $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';

    /**
     * 搜索短剧
     * @param string $text 搜索关键词
     * @return array|false
     */
    public function search($text)
    {
        if (empty($text)) {
            return false;
        }

        $url = $this->baseUrl . '?text=' . urlencode($text);
        return $this->makeRequest($url);
    }

    /**
     * 获取全部短剧列表
     * @return array|false
     */
    public function getList()
    {
        $url = $this->baseUrl . '?list';
        return $this->makeRequest($url);
    }

    /**
     * 获取今日更新的短剧
     * @return array|false
     */
    public function getToday()
    {
        $url = $this->baseUrl . '?today';
        return $this->makeRequest($url);
    }

    /**
     * 发起HTTP请求
     * @param string $url
     * @return array|false
     */
    private function makeRequest($url)
    {
        // 如果网络不可用，返回模拟数据用于演示
        if (!$this->isNetworkAvailable()) {
            return $this->getMockData($url);
        }

        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_USERAGENT => $this->userAgent,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTPHEADER => [
                'Accept: application/json',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Cache-Control: no-cache',
                'Pragma: no-cache'
            ]
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            error_log("DuanjuApi cURL Error: " . $error);
            // 网络错误时返回模拟数据
            return $this->getMockData($url);
        }

        if ($httpCode !== 200) {
            error_log("DuanjuApi HTTP Error: " . $httpCode);
            return $this->getMockData($url);
        }

        $data = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("DuanjuApi JSON Error: " . json_last_error_msg());
            return $this->getMockData($url);
        }

        return $data;
    }

    /**
     * 检查网络是否可用
     * @return bool
     */
    private function isNetworkAvailable()
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->baseUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 5,
            CURLOPT_NOBODY => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return $result !== false && $httpCode === 200;
    }

    /**
     * 获取模拟数据用于演示
     * @param string $url
     * @return array
     */
    private function getMockData($url)
    {
        $mockData = [
            'code' => 200,
            'msg' => '演示数据（网络不可用）',
            'data' => []
        ];

        if (strpos($url, 'today') !== false) {
            $mockData['msg'] = '今日更新（演示数据）';
            $mockData['data'] = [
                [
                    'name' => '演示短剧1-霸道总裁爱上我（完整版）',
                    'link' => 'https://pan.quark.cn/s/demo001',
                    'time' => date('Y-m-d H:i:s')
                ],
                [
                    'name' => '演示短剧2-重生之商业帝国（88集）',
                    'link' => 'https://pan.quark.cn/s/demo002',
                    'time' => date('Y-m-d H:i:s')
                ]
            ];
        } elseif (strpos($url, 'list') !== false) {
            $mockData['msg'] = '列表获取成功（演示数据）';
            $mockData['data'] = [
                [
                    'name' => '演示短剧3-都市修仙传（全集）',
                    'link' => 'https://pan.quark.cn/s/demo003',
                    'time' => date('Y-m-d H:i:s')
                ],
                [
                    'name' => '演示短剧4-穿越之王妃很忙（完结）',
                    'link' => 'https://pan.quark.cn/s/demo004',
                    'time' => date('Y-m-d H:i:s')
                ]
            ];
        } elseif (strpos($url, 'text=') !== false) {
            $mockData['msg'] = '搜索成功（演示数据）';
            $mockData['data'] = [
                [
                    'name' => '演示短剧5-搜索结果示例（高清版）',
                    'link' => 'https://pan.quark.cn/s/demo005',
                    'time' => date('Y-m-d H:i:s')
                ]
            ];
        }

        return $mockData;
    }

    /**
     * 格式化API返回的数据为转存格式
     * @param array $apiData API返回的数据
     * @return array 格式化后的数据
     */
    public function formatForSave($apiData)
    {
        $result = [];
        
        if (!isset($apiData['code']) || $apiData['code'] !== 200) {
            return $result;
        }

        if (!isset($apiData['data']) || !is_array($apiData['data'])) {
            return $result;
        }

        foreach ($apiData['data'] as $item) {
            if (isset($item['name']) && isset($item['link'])) {
                $result[] = [
                    'name' => $item['name'],
                    'url' => $item['link'],
                    'time' => $item['time'] ?? date('Y-m-d H:i:s')
                ];
            }
        }

        return $result;
    }

    /**
     * 将API数据保存为文件格式
     * @param array $apiData API返回的数据
     * @param string $filename 保存的文件名
     * @return bool
     */
    public function saveToFile($apiData, $filename)
    {
        $formattedData = $this->formatForSave($apiData);
        
        if (empty($formattedData)) {
            return false;
        }

        $content = '';
        foreach ($formattedData as $item) {
            $content .= $item['name'] . ' ' . $item['url'] . "\n";
        }

        return file_put_contents($filename, $content) !== false;
    }

    /**
     * 获取API状态信息
     * @return array
     */
    public function getStatus()
    {
        $startTime = microtime(true);
        $testData = $this->makeRequest($this->baseUrl . '?today');
        $endTime = microtime(true);
        
        $responseTime = round(($endTime - $startTime) * 1000, 2);
        
        return [
            'available' => $testData !== false,
            'response_time' => $responseTime,
            'last_check' => date('Y-m-d H:i:s'),
            'base_url' => $this->baseUrl
        ];
    }

    /**
     * 设置请求超时时间
     * @param int $timeout 超时时间（秒）
     */
    public function setTimeout($timeout)
    {
        $this->timeout = max(5, min(120, (int)$timeout));
    }

    /**
     * 设置User-Agent
     * @param string $userAgent
     */
    public function setUserAgent($userAgent)
    {
        $this->userAgent = $userAgent;
    }

    /**
     * 批量处理短剧数据
     * @param array $apiData API返回的数据
     * @param callable $callback 处理每个短剧的回调函数
     * @return array 处理结果
     */
    public function batchProcess($apiData, $callback)
    {
        $results = [];
        $formattedData = $this->formatForSave($apiData);
        
        foreach ($formattedData as $index => $item) {
            try {
                $result = call_user_func($callback, $item, $index);
                $results[] = [
                    'item' => $item,
                    'result' => $result,
                    'success' => true
                ];
            } catch (Exception $e) {
                $results[] = [
                    'item' => $item,
                    'result' => $e->getMessage(),
                    'success' => false
                ];
            }
        }
        
        return $results;
    }
}
?>
