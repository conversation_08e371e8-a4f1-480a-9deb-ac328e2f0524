@echo off
chcp 65001 >nul
title 端口配置检查

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      端口配置检查                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 检查端口6666是否可用...
echo.

:: 检查端口是否被占用
netstat -an | findstr ":6666" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  端口6666已被占用！
    echo.
    echo 占用端口6666的进程：
    netstat -ano | findstr ":6666"
    echo.
    echo 💡 解决方案：
    echo 1. 关闭占用端口的程序
    echo 2. 或者修改配置使用其他端口
) else (
    echo ✅ 端口6666可用
)

echo.
echo 📋 当前配置的访问地址：
echo    主页面: http://localhost:6666
echo    配置页面: http://localhost:6666/config
echo.

echo 🌐 测试网络连接...
ping -n 1 localhost >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 本地网络连接正常
) else (
    echo ❌ 本地网络连接异常
)

echo.
echo ════════════════════════════════════════════════════════════════
pause
