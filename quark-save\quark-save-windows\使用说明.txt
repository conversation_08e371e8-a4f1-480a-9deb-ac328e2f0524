# 夸克网盘自动化工具 - 使用说明

## 简介
这是一个用于夸克网盘自动化操作的Windows可执行程序，支持自动签到、转存、分享等功能。

## 使用前准备

### 1. 获取夸克网盘Cookie
1. 打开浏览器，登录夸克网盘 (https://pan.quark.cn)
2. 按F12打开开发者工具
3. 切换到"网络"(Network)标签
4. 刷新页面
5. 在请求列表中找到任意一个请求，查看请求头中的Cookie
6. 复制完整的Cookie内容
7. 将Cookie内容保存到程序目录下的 cookie.txt 文件中

### 2. 运行程序
双击 quark-save.bat 文件即可启动程序

## 功能说明

### 1. 自动签到
- 自动完成夸克网盘每日签到
- 显示账号信息和签到状态

### 2. 转存资源
- 支持从txt、csv、xlsx、xls文件批量转存资源
- 文件格式：每行包含"资源名称 夸克链接"
- 示例：
  ```
  电影名称1 https://pan.quark.cn/s/xxxxxx
  电影名称2 https://pan.quark.cn/s/yyyyyy
  ```

### 3. 分享资源
- 批量分享网盘中的文件夹
- 需要先执行"同步目录"功能

### 4. 同步目录
- 同步网盘目录结构到本地
- 为分享功能做准备

### 5. 自动更新全部短剧
- 从云端接口获取所有短剧资源并自动转存

### 6. 自动更新每日新增短剧
- 从云端接口获取当日新增短剧资源并自动转存

### 7. 搜索并转存短剧
- 根据关键词搜索短剧并自动转存

## 注意事项

1. 首次使用前必须配置cookie.txt文件
2. Cookie有时效性，如果出现登录失败请重新获取
3. 转存操作有频率限制，建议适当间隔
4. 程序会自动处理网络请求间隔，避免被限制

## 文件结构
```
quark-save/
├── quark-save.bat          # 主启动程序
├── QuarkService.php        # 核心功能脚本
├── Quark.php              # 夸克API封装
├── common.php             # 公共函数
├── composer.json          # 依赖配置
├── cookie.txt             # Cookie配置文件（需要手动创建）
├── php/                   # PHP运行时
├── vendor/                # 依赖库
└── 使用说明.txt           # 本文件
```

## 常见问题

### Q: 提示"cookie无效"怎么办？
A: 重新获取Cookie并更新cookie.txt文件

### Q: 转存失败怎么办？
A: 检查网络连接，确认链接有效，适当降低操作频率

### Q: 程序无法启动怎么办？
A: 确保所有文件完整，特别是php目录下的文件

## 技术支持
如有问题请访问项目主页：https://github.com/henggedaren/quark-save
