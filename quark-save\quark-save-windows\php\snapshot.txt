This snapshot was automatically generated on
Wed, 04 Jun 2025 21:51:40 +0000

Version: 8.3.22
Branch: HEAD
Build: C:\a\php-ftw\php-ftw\php\vs16\x64\obj\Release

Built-in Extensions
===========================
Core
bcmath
calendar
ctype
date
filter
hash
iconv
json
SPL
pcre
readline
Reflection
session
standard
mysqlnd
tokenizer
zip
zlib
libxml
dom
PDO
openssl
SimpleXML
xml
xmlreader
xmlwriter
curl
ftp
sqlite3
Phar
mbstring
mysqli


Dependency information:
Module: php_curl.dll
===========================
	libcrypto-3-x64.dll
	libssl-3-x64.dll
	libssh2.dll
	nghttp2.dll

Module: libssl-3-x64.dll
===========================
	libcrypto-3-x64.dll

Module: libssh2.dll
===========================
	libcrypto-3-x64.dll

Module: php_enchant.dll
===========================
	libenchant2.dll

Module: libenchant2.dll
===========================
	glib-2.dll
	gmodule-2.dll

Module: gmodule-2.dll
===========================
	glib-2.dll

Module: php_ftp.dll
===========================
	libcrypto-3-x64.dll
	libssl-3-x64.dll

Module: php_intl.dll
===========================
	icuin72.dll
	icuio72.dll
	icuuc72.dll
	vcruntime140_1.dll

Module: icuin72.dll
===========================
	icuuc72.dll
	vcruntime140_1.dll

Module: icuuc72.dll
===========================
	icudt72.dll
	vcruntime140_1.dll

Module: icuio72.dll
===========================
	icuuc72.dll
	icuin72.dll
	vcruntime140_1.dll

Module: php_ldap.dll
===========================
	libsasl.dll
	libcrypto-3-x64.dll
	libssl-3-x64.dll

Module: libsasl.dll
===========================
	libcrypto-3-x64.dll

Module: php_openssl.dll
===========================
	libcrypto-3-x64.dll
	libssl-3-x64.dll

Module: php_pgsql.dll
===========================
	libpq.dll

Module: libpq.dll
===========================
	libssl-3-x64.dll
	libcrypto-3-x64.dll

Module: php_snmp.dll
===========================
	libcrypto-3-x64.dll

Module: php_sodium.dll
===========================
	libsodium.dll

Module: php_sqlite3.dll
===========================
	libsqlite3.dll

Module: php_pdo_pgsql.dll
===========================
	libpq.dll

Module: php_pdo_sqlite.dll
===========================
	libsqlite3.dll

