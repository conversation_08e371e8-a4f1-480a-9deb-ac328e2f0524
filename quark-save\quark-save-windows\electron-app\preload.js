const { contextBridge, ipcRenderer } = require('electron');

// 向渲染进程暴露安全的API
contextBridge.exposeInMainWorld('electronAPI', {
    // 获取应用版本
    getAppVersion: () => ipcRenderer.invoke('get-app-version'),
    
    // 显示消息框
    showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
    
    // 应用信息
    platform: process.platform,
    isElectron: true
});

// 页面加载完成后的处理
window.addEventListener('DOMContentLoaded', () => {
    // 可以在这里添加一些页面增强功能
    console.log('Electron preload script loaded');
    
    // 添加一个标识，表明这是在Electron环境中运行
    document.body.classList.add('electron-app');
    
    // 可以添加一些桌面应用特有的样式或功能
    const style = document.createElement('style');
    style.textContent = `
        .electron-app {
            /* 桌面应用特有的样式 */
        }
        
        /* 隐藏一些在桌面应用中不需要的元素 */
        .web-only {
            display: none !important;
        }
        
        /* 添加桌面应用的标识 */
        .app-header::after {
            content: " (桌面版)";
            font-size: 0.8em;
            opacity: 0.7;
        }
    `;
    document.head.appendChild(style);
});
