@echo off
setlocal enabledelayedexpansion

:: Set script directory
set "SCRIPT_DIR=%~dp0"
set "TASK_NAME=QuarkDailyUpdate"
set "TASK_SCRIPT=%SCRIPT_DIR%每日自动更新.bat"

echo ========================================
echo         Create Scheduled Task
echo ========================================
echo.
echo This will create a daily scheduled task to automatically
echo update drama resources from API.
echo.
echo Task Name: %TASK_NAME%
echo Script: %TASK_SCRIPT%
echo Schedule: Daily at 9:00 AM
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: This script requires administrator privileges.
    echo Please run as administrator.
    echo.
    pause
    exit /b 1
)

:: Check if task already exists
schtasks /query /tn "%TASK_NAME%" >nul 2>&1
if %errorlevel% equ 0 (
    echo Task already exists. Do you want to recreate it? (Y/N)
    set /p choice=
    if /i "!choice!" neq "Y" (
        echo Operation cancelled.
        pause
        exit /b 0
    )
    
    echo Deleting existing task...
    schtasks /delete /tn "%TASK_NAME%" /f >nul 2>&1
)

:: Create the scheduled task
echo Creating scheduled task...
schtasks /create /tn "%TASK_NAME%" /tr "\"%TASK_SCRIPT%\" auto" /sc daily /st 09:00 /ru "SYSTEM" /rl highest /f

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo         Task Created Successfully
    echo ========================================
    echo.
    echo Task Name: %TASK_NAME%
    echo Schedule: Daily at 9:00 AM
    echo.
    echo You can modify the schedule using Windows Task Scheduler:
    echo 1. Press Win+R, type "taskschd.msc" and press Enter
    echo 2. Find "%TASK_NAME%" in the task list
    echo 3. Right-click and select "Properties" to modify
    echo.
    echo To test the task manually:
    echo schtasks /run /tn "%TASK_NAME%"
    echo.
) else (
    echo.
    echo ========================================
    echo         Task Creation Failed
    echo ========================================
    echo.
    echo Please check the error messages above.
    echo.
)

pause
