<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit42eb5285e47ec6fe7fada0508e5a4977
{
    public static $prefixLengthsPsr4 = array (
        'q' => 
        array (
            'quark\\' => 6,
        ),
        'O' => 
        array (
            'OpenSpout\\' => 10,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'quark\\' => 
        array (
            0 => __DIR__ . '/../..' . '/',
        ),
        'OpenSpout\\' => 
        array (
            0 => __DIR__ . '/..' . '/openspout/openspout/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Console_Table' => __DIR__ . '/..' . '/pear/console_table/Table.php',
        'OpenSpout\\Common\\Entity\\Cell' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Cell.php',
        'OpenSpout\\Common\\Entity\\Cell\\BooleanCell' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Cell/BooleanCell.php',
        'OpenSpout\\Common\\Entity\\Cell\\DateIntervalCell' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Cell/DateIntervalCell.php',
        'OpenSpout\\Common\\Entity\\Cell\\DateTimeCell' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Cell/DateTimeCell.php',
        'OpenSpout\\Common\\Entity\\Cell\\EmptyCell' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Cell/EmptyCell.php',
        'OpenSpout\\Common\\Entity\\Cell\\ErrorCell' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Cell/ErrorCell.php',
        'OpenSpout\\Common\\Entity\\Cell\\FormulaCell' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Cell/FormulaCell.php',
        'OpenSpout\\Common\\Entity\\Cell\\NumericCell' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Cell/NumericCell.php',
        'OpenSpout\\Common\\Entity\\Cell\\StringCell' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Cell/StringCell.php',
        'OpenSpout\\Common\\Entity\\Comment\\Comment' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Comment/Comment.php',
        'OpenSpout\\Common\\Entity\\Comment\\TextRun' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Comment/TextRun.php',
        'OpenSpout\\Common\\Entity\\Row' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Row.php',
        'OpenSpout\\Common\\Entity\\Style\\Border' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Style/Border.php',
        'OpenSpout\\Common\\Entity\\Style\\BorderPart' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Style/BorderPart.php',
        'OpenSpout\\Common\\Entity\\Style\\CellAlignment' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Style/CellAlignment.php',
        'OpenSpout\\Common\\Entity\\Style\\CellVerticalAlignment' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Style/CellVerticalAlignment.php',
        'OpenSpout\\Common\\Entity\\Style\\Color' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Style/Color.php',
        'OpenSpout\\Common\\Entity\\Style\\Style' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Entity/Style/Style.php',
        'OpenSpout\\Common\\Exception\\EncodingConversionException' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Exception/EncodingConversionException.php',
        'OpenSpout\\Common\\Exception\\IOException' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Exception/IOException.php',
        'OpenSpout\\Common\\Exception\\InvalidArgumentException' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Exception/InvalidArgumentException.php',
        'OpenSpout\\Common\\Exception\\InvalidColorException' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Exception/InvalidColorException.php',
        'OpenSpout\\Common\\Exception\\OpenSpoutException' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Exception/OpenSpoutException.php',
        'OpenSpout\\Common\\Exception\\UnsupportedTypeException' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Exception/UnsupportedTypeException.php',
        'OpenSpout\\Common\\Helper\\EncodingHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Helper/EncodingHelper.php',
        'OpenSpout\\Common\\Helper\\Escaper\\EscaperInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Helper/Escaper/EscaperInterface.php',
        'OpenSpout\\Common\\Helper\\Escaper\\ODS' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Helper/Escaper/ODS.php',
        'OpenSpout\\Common\\Helper\\Escaper\\XLSX' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Helper/Escaper/XLSX.php',
        'OpenSpout\\Common\\Helper\\FileSystemHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Helper/FileSystemHelper.php',
        'OpenSpout\\Common\\Helper\\FileSystemHelperInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Helper/FileSystemHelperInterface.php',
        'OpenSpout\\Common\\Helper\\StringHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Common/Helper/StringHelper.php',
        'OpenSpout\\Common\\TempFolderOptionTrait' => __DIR__ . '/..' . '/openspout/openspout/src/Common/TempFolderOptionTrait.php',
        'OpenSpout\\Reader\\AbstractReader' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/AbstractReader.php',
        'OpenSpout\\Reader\\CSV\\Options' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/CSV/Options.php',
        'OpenSpout\\Reader\\CSV\\Reader' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/CSV/Reader.php',
        'OpenSpout\\Reader\\CSV\\RowIterator' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/CSV/RowIterator.php',
        'OpenSpout\\Reader\\CSV\\Sheet' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/CSV/Sheet.php',
        'OpenSpout\\Reader\\CSV\\SheetIterator' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/CSV/SheetIterator.php',
        'OpenSpout\\Reader\\Common\\ColumnWidth' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/Common/ColumnWidth.php',
        'OpenSpout\\Reader\\Common\\Creator\\ReaderFactory' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/Common/Creator/ReaderFactory.php',
        'OpenSpout\\Reader\\Common\\Manager\\RowManager' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/Common/Manager/RowManager.php',
        'OpenSpout\\Reader\\Common\\XMLProcessor' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/Common/XMLProcessor.php',
        'OpenSpout\\Reader\\Exception\\InvalidValueException' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/Exception/InvalidValueException.php',
        'OpenSpout\\Reader\\Exception\\IteratorNotRewindableException' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/Exception/IteratorNotRewindableException.php',
        'OpenSpout\\Reader\\Exception\\NoSheetsFoundException' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/Exception/NoSheetsFoundException.php',
        'OpenSpout\\Reader\\Exception\\ReaderException' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/Exception/ReaderException.php',
        'OpenSpout\\Reader\\Exception\\ReaderNotOpenedException' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/Exception/ReaderNotOpenedException.php',
        'OpenSpout\\Reader\\Exception\\SharedStringNotFoundException' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/Exception/SharedStringNotFoundException.php',
        'OpenSpout\\Reader\\Exception\\XMLProcessingException' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/Exception/XMLProcessingException.php',
        'OpenSpout\\Reader\\ODS\\Helper\\CellValueFormatter' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/ODS/Helper/CellValueFormatter.php',
        'OpenSpout\\Reader\\ODS\\Helper\\SettingsHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/ODS/Helper/SettingsHelper.php',
        'OpenSpout\\Reader\\ODS\\Options' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/ODS/Options.php',
        'OpenSpout\\Reader\\ODS\\Reader' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/ODS/Reader.php',
        'OpenSpout\\Reader\\ODS\\RowIterator' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/ODS/RowIterator.php',
        'OpenSpout\\Reader\\ODS\\Sheet' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/ODS/Sheet.php',
        'OpenSpout\\Reader\\ODS\\SheetIterator' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/ODS/SheetIterator.php',
        'OpenSpout\\Reader\\ReaderInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/ReaderInterface.php',
        'OpenSpout\\Reader\\RowIteratorInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/RowIteratorInterface.php',
        'OpenSpout\\Reader\\SheetInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/SheetInterface.php',
        'OpenSpout\\Reader\\SheetIteratorInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/SheetIteratorInterface.php',
        'OpenSpout\\Reader\\SheetWithMergeCellsInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/SheetWithMergeCellsInterface.php',
        'OpenSpout\\Reader\\SheetWithVisibilityInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/SheetWithVisibilityInterface.php',
        'OpenSpout\\Reader\\Wrapper\\XMLInternalErrorsHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/Wrapper/XMLInternalErrorsHelper.php',
        'OpenSpout\\Reader\\Wrapper\\XMLReader' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/Wrapper/XMLReader.php',
        'OpenSpout\\Reader\\XLSX\\Helper\\CellHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Helper/CellHelper.php',
        'OpenSpout\\Reader\\XLSX\\Helper\\CellValueFormatter' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Helper/CellValueFormatter.php',
        'OpenSpout\\Reader\\XLSX\\Helper\\DateFormatHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Helper/DateFormatHelper.php',
        'OpenSpout\\Reader\\XLSX\\Helper\\DateIntervalFormatHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Helper/DateIntervalFormatHelper.php',
        'OpenSpout\\Reader\\XLSX\\Manager\\SharedStringsCaching\\CachingStrategyFactory' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Manager/SharedStringsCaching/CachingStrategyFactory.php',
        'OpenSpout\\Reader\\XLSX\\Manager\\SharedStringsCaching\\CachingStrategyFactoryInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Manager/SharedStringsCaching/CachingStrategyFactoryInterface.php',
        'OpenSpout\\Reader\\XLSX\\Manager\\SharedStringsCaching\\CachingStrategyInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Manager/SharedStringsCaching/CachingStrategyInterface.php',
        'OpenSpout\\Reader\\XLSX\\Manager\\SharedStringsCaching\\FileBasedStrategy' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Manager/SharedStringsCaching/FileBasedStrategy.php',
        'OpenSpout\\Reader\\XLSX\\Manager\\SharedStringsCaching\\InMemoryStrategy' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Manager/SharedStringsCaching/InMemoryStrategy.php',
        'OpenSpout\\Reader\\XLSX\\Manager\\SharedStringsCaching\\MemoryLimit' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Manager/SharedStringsCaching/MemoryLimit.php',
        'OpenSpout\\Reader\\XLSX\\Manager\\SharedStringsManager' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Manager/SharedStringsManager.php',
        'OpenSpout\\Reader\\XLSX\\Manager\\SheetManager' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Manager/SheetManager.php',
        'OpenSpout\\Reader\\XLSX\\Manager\\StyleManager' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Manager/StyleManager.php',
        'OpenSpout\\Reader\\XLSX\\Manager\\StyleManagerInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Manager/StyleManagerInterface.php',
        'OpenSpout\\Reader\\XLSX\\Manager\\WorkbookRelationshipsManager' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Manager/WorkbookRelationshipsManager.php',
        'OpenSpout\\Reader\\XLSX\\Options' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Options.php',
        'OpenSpout\\Reader\\XLSX\\Reader' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Reader.php',
        'OpenSpout\\Reader\\XLSX\\RowIterator' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/RowIterator.php',
        'OpenSpout\\Reader\\XLSX\\Sheet' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/Sheet.php',
        'OpenSpout\\Reader\\XLSX\\SheetHeaderReader' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/SheetHeaderReader.php',
        'OpenSpout\\Reader\\XLSX\\SheetIterator' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/SheetIterator.php',
        'OpenSpout\\Reader\\XLSX\\SheetMergeCellsReader' => __DIR__ . '/..' . '/openspout/openspout/src/Reader/XLSX/SheetMergeCellsReader.php',
        'OpenSpout\\Writer\\AbstractWriter' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/AbstractWriter.php',
        'OpenSpout\\Writer\\AbstractWriterMultiSheets' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/AbstractWriterMultiSheets.php',
        'OpenSpout\\Writer\\AutoFilter' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/AutoFilter.php',
        'OpenSpout\\Writer\\CSV\\Options' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/CSV/Options.php',
        'OpenSpout\\Writer\\CSV\\Writer' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/CSV/Writer.php',
        'OpenSpout\\Writer\\Common\\AbstractOptions' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/AbstractOptions.php',
        'OpenSpout\\Writer\\Common\\ColumnWidth' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/ColumnWidth.php',
        'OpenSpout\\Writer\\Common\\Creator\\WriterFactory' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Creator/WriterFactory.php',
        'OpenSpout\\Writer\\Common\\Entity\\Sheet' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Entity/Sheet.php',
        'OpenSpout\\Writer\\Common\\Entity\\Workbook' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Entity/Workbook.php',
        'OpenSpout\\Writer\\Common\\Entity\\Worksheet' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Entity/Worksheet.php',
        'OpenSpout\\Writer\\Common\\Helper\\CellHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Helper/CellHelper.php',
        'OpenSpout\\Writer\\Common\\Helper\\FileSystemWithRootFolderHelperInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Helper/FileSystemWithRootFolderHelperInterface.php',
        'OpenSpout\\Writer\\Common\\Helper\\ZipHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Helper/ZipHelper.php',
        'OpenSpout\\Writer\\Common\\Manager\\AbstractWorkbookManager' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Manager/AbstractWorkbookManager.php',
        'OpenSpout\\Writer\\Common\\Manager\\RegisteredStyle' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Manager/RegisteredStyle.php',
        'OpenSpout\\Writer\\Common\\Manager\\SheetManager' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Manager/SheetManager.php',
        'OpenSpout\\Writer\\Common\\Manager\\Style\\AbstractStyleManager' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Manager/Style/AbstractStyleManager.php',
        'OpenSpout\\Writer\\Common\\Manager\\Style\\AbstractStyleRegistry' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Manager/Style/AbstractStyleRegistry.php',
        'OpenSpout\\Writer\\Common\\Manager\\Style\\PossiblyUpdatedStyle' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Manager/Style/PossiblyUpdatedStyle.php',
        'OpenSpout\\Writer\\Common\\Manager\\Style\\StyleManagerInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Manager/Style/StyleManagerInterface.php',
        'OpenSpout\\Writer\\Common\\Manager\\Style\\StyleMerger' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Manager/Style/StyleMerger.php',
        'OpenSpout\\Writer\\Common\\Manager\\WorkbookManagerInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Manager/WorkbookManagerInterface.php',
        'OpenSpout\\Writer\\Common\\Manager\\WorksheetManagerInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Common/Manager/WorksheetManagerInterface.php',
        'OpenSpout\\Writer\\Exception\\Border\\InvalidNameException' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Exception/Border/InvalidNameException.php',
        'OpenSpout\\Writer\\Exception\\Border\\InvalidStyleException' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Exception/Border/InvalidStyleException.php',
        'OpenSpout\\Writer\\Exception\\Border\\InvalidWidthException' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Exception/Border/InvalidWidthException.php',
        'OpenSpout\\Writer\\Exception\\InvalidSheetNameException' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Exception/InvalidSheetNameException.php',
        'OpenSpout\\Writer\\Exception\\SheetNotFoundException' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Exception/SheetNotFoundException.php',
        'OpenSpout\\Writer\\Exception\\WriterAlreadyOpenedException' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Exception/WriterAlreadyOpenedException.php',
        'OpenSpout\\Writer\\Exception\\WriterException' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Exception/WriterException.php',
        'OpenSpout\\Writer\\Exception\\WriterNotOpenedException' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/Exception/WriterNotOpenedException.php',
        'OpenSpout\\Writer\\ODS\\Helper\\BorderHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/ODS/Helper/BorderHelper.php',
        'OpenSpout\\Writer\\ODS\\Helper\\FileSystemHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/ODS/Helper/FileSystemHelper.php',
        'OpenSpout\\Writer\\ODS\\Manager\\Style\\StyleManager' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/ODS/Manager/Style/StyleManager.php',
        'OpenSpout\\Writer\\ODS\\Manager\\Style\\StyleRegistry' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/ODS/Manager/Style/StyleRegistry.php',
        'OpenSpout\\Writer\\ODS\\Manager\\WorkbookManager' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/ODS/Manager/WorkbookManager.php',
        'OpenSpout\\Writer\\ODS\\Manager\\WorksheetManager' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/ODS/Manager/WorksheetManager.php',
        'OpenSpout\\Writer\\ODS\\Options' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/ODS/Options.php',
        'OpenSpout\\Writer\\ODS\\Writer' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/ODS/Writer.php',
        'OpenSpout\\Writer\\WriterInterface' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/WriterInterface.php',
        'OpenSpout\\Writer\\XLSX\\Entity\\SheetView' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Entity/SheetView.php',
        'OpenSpout\\Writer\\XLSX\\Helper\\BorderHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Helper/BorderHelper.php',
        'OpenSpout\\Writer\\XLSX\\Helper\\DateHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Helper/DateHelper.php',
        'OpenSpout\\Writer\\XLSX\\Helper\\DateIntervalHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Helper/DateIntervalHelper.php',
        'OpenSpout\\Writer\\XLSX\\Helper\\FileSystemHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Helper/FileSystemHelper.php',
        'OpenSpout\\Writer\\XLSX\\Helper\\PasswordHashHelper' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Helper/PasswordHashHelper.php',
        'OpenSpout\\Writer\\XLSX\\Manager\\CommentsManager' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Manager/CommentsManager.php',
        'OpenSpout\\Writer\\XLSX\\Manager\\SharedStringsManager' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Manager/SharedStringsManager.php',
        'OpenSpout\\Writer\\XLSX\\Manager\\Style\\StyleManager' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Manager/Style/StyleManager.php',
        'OpenSpout\\Writer\\XLSX\\Manager\\Style\\StyleRegistry' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Manager/Style/StyleRegistry.php',
        'OpenSpout\\Writer\\XLSX\\Manager\\WorkbookManager' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Manager/WorkbookManager.php',
        'OpenSpout\\Writer\\XLSX\\Manager\\WorksheetManager' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Manager/WorksheetManager.php',
        'OpenSpout\\Writer\\XLSX\\MergeCell' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/MergeCell.php',
        'OpenSpout\\Writer\\XLSX\\Options' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Options.php',
        'OpenSpout\\Writer\\XLSX\\Options\\HeaderFooter' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Options/HeaderFooter.php',
        'OpenSpout\\Writer\\XLSX\\Options\\PageMargin' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Options/PageMargin.php',
        'OpenSpout\\Writer\\XLSX\\Options\\PageOrientation' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Options/PageOrientation.php',
        'OpenSpout\\Writer\\XLSX\\Options\\PageSetup' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Options/PageSetup.php',
        'OpenSpout\\Writer\\XLSX\\Options\\PaperSize' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Options/PaperSize.php',
        'OpenSpout\\Writer\\XLSX\\Options\\SheetProtection' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Options/SheetProtection.php',
        'OpenSpout\\Writer\\XLSX\\Options\\WorkbookProtection' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Options/WorkbookProtection.php',
        'OpenSpout\\Writer\\XLSX\\Properties' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Properties.php',
        'OpenSpout\\Writer\\XLSX\\Writer' => __DIR__ . '/..' . '/openspout/openspout/src/Writer/XLSX/Writer.php',
        'quark\\Quark' => __DIR__ . '/../..' . '/Quark.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit42eb5285e47ec6fe7fada0508e5a4977::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit42eb5285e47ec6fe7fada0508e5a4977::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit42eb5285e47ec6fe7fada0508e5a4977::$classMap;

        }, null, ClassLoader::class);
    }
}
