@echo off
setlocal enabledelayedexpansion

echo ========================================
echo     夸克网盘工具桌面版 - 环境设置
echo ========================================
echo.

:: 检查Node.js是否已安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Node.js，请先安装Node.js
    echo.
    echo 请访问 https://nodejs.org 下载并安装Node.js
    echo 建议安装LTS版本
    echo.
    pause
    exit /b 1
)

echo [信息] 检测到Node.js版本:
node --version

echo.
echo [信息] 开始安装依赖包...
echo.

:: 安装npm依赖
call npm install

if %errorlevel% neq 0 (
    echo.
    echo [错误] 依赖安装失败，请检查网络连接或尝试以下命令:
    echo npm config set registry https://registry.npmmirror.com
    echo.
    pause
    exit /b 1
)

echo.
echo [成功] 依赖安装完成！
echo.
echo 可用命令:
echo   npm start      - 开发模式运行
echo   npm run build  - 构建应用
echo.
echo 现在可以运行 'npm start' 来启动应用
echo.
pause
