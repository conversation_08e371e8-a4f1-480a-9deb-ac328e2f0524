# 夸克网盘自动化工具

## 🎯 项目介绍

这是一个功能强大的夸克网盘自动化管理工具，支持自动签到、目录同步、文件分享、短剧更新等功能。

## 📁 项目结构

```
quark-save/
├── quark-save-windows/     # 主程序目录（推荐使用）
│   ├── 启动工具.bat        # 一键启动（推荐）
│   ├── 启动夸克工具.bat    # 完整启动脚本
│   ├── 使用说明.md         # 详细使用说明
│   └── ...                # 其他程序文件
└── *.bat                  # 旧版批处理脚本
```

## 🚀 快速开始

### 推荐方式（Web界面版）
```
1. 进入 quark-save-windows 目录
2. 双击运行：启动工具.bat
3. 浏览器自动打开 http://127.0.0.1:6666
4. 首次使用请先配置Cookie
```

### 传统方式（命令行版）
```
1. 在根目录运行对应的批处理文件
2. 如：1、自动签到.bat
```

## ⚙️ 环境要求

- Windows 10/11
- 内置PHP运行时（无需额外安装）
- 现代浏览器（Chrome、Edge、Firefox等）

## 🌐 Web界面功能

- **主页面**: http://127.0.0.1:6666
  - 功能概览
  - 快速操作
  - 状态监控

- **配置页面**: http://127.0.0.1:6666/config
  - Cookie配置
  - 参数设置
  - 系统配置

## 🔧 主要功能

### 自动化任务
- ✅ 每日自动签到
- 📁 网盘目录同步
- 🔗 批量文件分享
- 📺 短剧资源更新
- ⏰ 定时任务管理

### Web管理界面
- 🌐 现代化Web界面
- ⚙️ 可视化配置管理
- 📊 实时状态监控
- 📝 日志查看功能

## 📖 详细说明

请查看 `quark-save-windows/使用说明.md` 获取完整的使用指南。

## 🔒 安全说明

- 所有数据仅存储在本地
- Cookie信息不会上传
- 开源透明，可自行审查代码

## 📞 技术支持

如遇问题：
1. 查看 `quark-save-windows/logs/` 目录中的日志
2. 确认Cookie配置正确
3. 检查网络连接状态

---

**推荐使用**: `quark-save-windows` 目录中的Web界面版本  
**端口**: 6666  
**版本**: v2.0
