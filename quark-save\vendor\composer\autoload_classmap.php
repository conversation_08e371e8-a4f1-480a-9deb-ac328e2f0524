<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Console_Table' => $vendorDir . '/pear/console_table/Table.php',
    'OpenSpout\\Common\\Entity\\Cell' => $vendorDir . '/openspout/openspout/src/Common/Entity/Cell.php',
    'OpenSpout\\Common\\Entity\\Cell\\BooleanCell' => $vendorDir . '/openspout/openspout/src/Common/Entity/Cell/BooleanCell.php',
    'OpenSpout\\Common\\Entity\\Cell\\DateIntervalCell' => $vendorDir . '/openspout/openspout/src/Common/Entity/Cell/DateIntervalCell.php',
    'OpenSpout\\Common\\Entity\\Cell\\DateTimeCell' => $vendorDir . '/openspout/openspout/src/Common/Entity/Cell/DateTimeCell.php',
    'OpenSpout\\Common\\Entity\\Cell\\EmptyCell' => $vendorDir . '/openspout/openspout/src/Common/Entity/Cell/EmptyCell.php',
    'OpenSpout\\Common\\Entity\\Cell\\ErrorCell' => $vendorDir . '/openspout/openspout/src/Common/Entity/Cell/ErrorCell.php',
    'OpenSpout\\Common\\Entity\\Cell\\FormulaCell' => $vendorDir . '/openspout/openspout/src/Common/Entity/Cell/FormulaCell.php',
    'OpenSpout\\Common\\Entity\\Cell\\NumericCell' => $vendorDir . '/openspout/openspout/src/Common/Entity/Cell/NumericCell.php',
    'OpenSpout\\Common\\Entity\\Cell\\StringCell' => $vendorDir . '/openspout/openspout/src/Common/Entity/Cell/StringCell.php',
    'OpenSpout\\Common\\Entity\\Comment\\Comment' => $vendorDir . '/openspout/openspout/src/Common/Entity/Comment/Comment.php',
    'OpenSpout\\Common\\Entity\\Comment\\TextRun' => $vendorDir . '/openspout/openspout/src/Common/Entity/Comment/TextRun.php',
    'OpenSpout\\Common\\Entity\\Row' => $vendorDir . '/openspout/openspout/src/Common/Entity/Row.php',
    'OpenSpout\\Common\\Entity\\Style\\Border' => $vendorDir . '/openspout/openspout/src/Common/Entity/Style/Border.php',
    'OpenSpout\\Common\\Entity\\Style\\BorderPart' => $vendorDir . '/openspout/openspout/src/Common/Entity/Style/BorderPart.php',
    'OpenSpout\\Common\\Entity\\Style\\CellAlignment' => $vendorDir . '/openspout/openspout/src/Common/Entity/Style/CellAlignment.php',
    'OpenSpout\\Common\\Entity\\Style\\CellVerticalAlignment' => $vendorDir . '/openspout/openspout/src/Common/Entity/Style/CellVerticalAlignment.php',
    'OpenSpout\\Common\\Entity\\Style\\Color' => $vendorDir . '/openspout/openspout/src/Common/Entity/Style/Color.php',
    'OpenSpout\\Common\\Entity\\Style\\Style' => $vendorDir . '/openspout/openspout/src/Common/Entity/Style/Style.php',
    'OpenSpout\\Common\\Exception\\EncodingConversionException' => $vendorDir . '/openspout/openspout/src/Common/Exception/EncodingConversionException.php',
    'OpenSpout\\Common\\Exception\\IOException' => $vendorDir . '/openspout/openspout/src/Common/Exception/IOException.php',
    'OpenSpout\\Common\\Exception\\InvalidArgumentException' => $vendorDir . '/openspout/openspout/src/Common/Exception/InvalidArgumentException.php',
    'OpenSpout\\Common\\Exception\\InvalidColorException' => $vendorDir . '/openspout/openspout/src/Common/Exception/InvalidColorException.php',
    'OpenSpout\\Common\\Exception\\OpenSpoutException' => $vendorDir . '/openspout/openspout/src/Common/Exception/OpenSpoutException.php',
    'OpenSpout\\Common\\Exception\\UnsupportedTypeException' => $vendorDir . '/openspout/openspout/src/Common/Exception/UnsupportedTypeException.php',
    'OpenSpout\\Common\\Helper\\EncodingHelper' => $vendorDir . '/openspout/openspout/src/Common/Helper/EncodingHelper.php',
    'OpenSpout\\Common\\Helper\\Escaper\\EscaperInterface' => $vendorDir . '/openspout/openspout/src/Common/Helper/Escaper/EscaperInterface.php',
    'OpenSpout\\Common\\Helper\\Escaper\\ODS' => $vendorDir . '/openspout/openspout/src/Common/Helper/Escaper/ODS.php',
    'OpenSpout\\Common\\Helper\\Escaper\\XLSX' => $vendorDir . '/openspout/openspout/src/Common/Helper/Escaper/XLSX.php',
    'OpenSpout\\Common\\Helper\\FileSystemHelper' => $vendorDir . '/openspout/openspout/src/Common/Helper/FileSystemHelper.php',
    'OpenSpout\\Common\\Helper\\FileSystemHelperInterface' => $vendorDir . '/openspout/openspout/src/Common/Helper/FileSystemHelperInterface.php',
    'OpenSpout\\Common\\Helper\\StringHelper' => $vendorDir . '/openspout/openspout/src/Common/Helper/StringHelper.php',
    'OpenSpout\\Common\\TempFolderOptionTrait' => $vendorDir . '/openspout/openspout/src/Common/TempFolderOptionTrait.php',
    'OpenSpout\\Reader\\AbstractReader' => $vendorDir . '/openspout/openspout/src/Reader/AbstractReader.php',
    'OpenSpout\\Reader\\CSV\\Options' => $vendorDir . '/openspout/openspout/src/Reader/CSV/Options.php',
    'OpenSpout\\Reader\\CSV\\Reader' => $vendorDir . '/openspout/openspout/src/Reader/CSV/Reader.php',
    'OpenSpout\\Reader\\CSV\\RowIterator' => $vendorDir . '/openspout/openspout/src/Reader/CSV/RowIterator.php',
    'OpenSpout\\Reader\\CSV\\Sheet' => $vendorDir . '/openspout/openspout/src/Reader/CSV/Sheet.php',
    'OpenSpout\\Reader\\CSV\\SheetIterator' => $vendorDir . '/openspout/openspout/src/Reader/CSV/SheetIterator.php',
    'OpenSpout\\Reader\\Common\\ColumnWidth' => $vendorDir . '/openspout/openspout/src/Reader/Common/ColumnWidth.php',
    'OpenSpout\\Reader\\Common\\Creator\\ReaderFactory' => $vendorDir . '/openspout/openspout/src/Reader/Common/Creator/ReaderFactory.php',
    'OpenSpout\\Reader\\Common\\Manager\\RowManager' => $vendorDir . '/openspout/openspout/src/Reader/Common/Manager/RowManager.php',
    'OpenSpout\\Reader\\Common\\XMLProcessor' => $vendorDir . '/openspout/openspout/src/Reader/Common/XMLProcessor.php',
    'OpenSpout\\Reader\\Exception\\InvalidValueException' => $vendorDir . '/openspout/openspout/src/Reader/Exception/InvalidValueException.php',
    'OpenSpout\\Reader\\Exception\\IteratorNotRewindableException' => $vendorDir . '/openspout/openspout/src/Reader/Exception/IteratorNotRewindableException.php',
    'OpenSpout\\Reader\\Exception\\NoSheetsFoundException' => $vendorDir . '/openspout/openspout/src/Reader/Exception/NoSheetsFoundException.php',
    'OpenSpout\\Reader\\Exception\\ReaderException' => $vendorDir . '/openspout/openspout/src/Reader/Exception/ReaderException.php',
    'OpenSpout\\Reader\\Exception\\ReaderNotOpenedException' => $vendorDir . '/openspout/openspout/src/Reader/Exception/ReaderNotOpenedException.php',
    'OpenSpout\\Reader\\Exception\\SharedStringNotFoundException' => $vendorDir . '/openspout/openspout/src/Reader/Exception/SharedStringNotFoundException.php',
    'OpenSpout\\Reader\\Exception\\XMLProcessingException' => $vendorDir . '/openspout/openspout/src/Reader/Exception/XMLProcessingException.php',
    'OpenSpout\\Reader\\ODS\\Helper\\CellValueFormatter' => $vendorDir . '/openspout/openspout/src/Reader/ODS/Helper/CellValueFormatter.php',
    'OpenSpout\\Reader\\ODS\\Helper\\SettingsHelper' => $vendorDir . '/openspout/openspout/src/Reader/ODS/Helper/SettingsHelper.php',
    'OpenSpout\\Reader\\ODS\\Options' => $vendorDir . '/openspout/openspout/src/Reader/ODS/Options.php',
    'OpenSpout\\Reader\\ODS\\Reader' => $vendorDir . '/openspout/openspout/src/Reader/ODS/Reader.php',
    'OpenSpout\\Reader\\ODS\\RowIterator' => $vendorDir . '/openspout/openspout/src/Reader/ODS/RowIterator.php',
    'OpenSpout\\Reader\\ODS\\Sheet' => $vendorDir . '/openspout/openspout/src/Reader/ODS/Sheet.php',
    'OpenSpout\\Reader\\ODS\\SheetIterator' => $vendorDir . '/openspout/openspout/src/Reader/ODS/SheetIterator.php',
    'OpenSpout\\Reader\\ReaderInterface' => $vendorDir . '/openspout/openspout/src/Reader/ReaderInterface.php',
    'OpenSpout\\Reader\\RowIteratorInterface' => $vendorDir . '/openspout/openspout/src/Reader/RowIteratorInterface.php',
    'OpenSpout\\Reader\\SheetInterface' => $vendorDir . '/openspout/openspout/src/Reader/SheetInterface.php',
    'OpenSpout\\Reader\\SheetIteratorInterface' => $vendorDir . '/openspout/openspout/src/Reader/SheetIteratorInterface.php',
    'OpenSpout\\Reader\\SheetWithMergeCellsInterface' => $vendorDir . '/openspout/openspout/src/Reader/SheetWithMergeCellsInterface.php',
    'OpenSpout\\Reader\\SheetWithVisibilityInterface' => $vendorDir . '/openspout/openspout/src/Reader/SheetWithVisibilityInterface.php',
    'OpenSpout\\Reader\\Wrapper\\XMLInternalErrorsHelper' => $vendorDir . '/openspout/openspout/src/Reader/Wrapper/XMLInternalErrorsHelper.php',
    'OpenSpout\\Reader\\Wrapper\\XMLReader' => $vendorDir . '/openspout/openspout/src/Reader/Wrapper/XMLReader.php',
    'OpenSpout\\Reader\\XLSX\\Helper\\CellHelper' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Helper/CellHelper.php',
    'OpenSpout\\Reader\\XLSX\\Helper\\CellValueFormatter' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Helper/CellValueFormatter.php',
    'OpenSpout\\Reader\\XLSX\\Helper\\DateFormatHelper' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Helper/DateFormatHelper.php',
    'OpenSpout\\Reader\\XLSX\\Helper\\DateIntervalFormatHelper' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Helper/DateIntervalFormatHelper.php',
    'OpenSpout\\Reader\\XLSX\\Manager\\SharedStringsCaching\\CachingStrategyFactory' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Manager/SharedStringsCaching/CachingStrategyFactory.php',
    'OpenSpout\\Reader\\XLSX\\Manager\\SharedStringsCaching\\CachingStrategyFactoryInterface' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Manager/SharedStringsCaching/CachingStrategyFactoryInterface.php',
    'OpenSpout\\Reader\\XLSX\\Manager\\SharedStringsCaching\\CachingStrategyInterface' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Manager/SharedStringsCaching/CachingStrategyInterface.php',
    'OpenSpout\\Reader\\XLSX\\Manager\\SharedStringsCaching\\FileBasedStrategy' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Manager/SharedStringsCaching/FileBasedStrategy.php',
    'OpenSpout\\Reader\\XLSX\\Manager\\SharedStringsCaching\\InMemoryStrategy' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Manager/SharedStringsCaching/InMemoryStrategy.php',
    'OpenSpout\\Reader\\XLSX\\Manager\\SharedStringsCaching\\MemoryLimit' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Manager/SharedStringsCaching/MemoryLimit.php',
    'OpenSpout\\Reader\\XLSX\\Manager\\SharedStringsManager' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Manager/SharedStringsManager.php',
    'OpenSpout\\Reader\\XLSX\\Manager\\SheetManager' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Manager/SheetManager.php',
    'OpenSpout\\Reader\\XLSX\\Manager\\StyleManager' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Manager/StyleManager.php',
    'OpenSpout\\Reader\\XLSX\\Manager\\StyleManagerInterface' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Manager/StyleManagerInterface.php',
    'OpenSpout\\Reader\\XLSX\\Manager\\WorkbookRelationshipsManager' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Manager/WorkbookRelationshipsManager.php',
    'OpenSpout\\Reader\\XLSX\\Options' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Options.php',
    'OpenSpout\\Reader\\XLSX\\Reader' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Reader.php',
    'OpenSpout\\Reader\\XLSX\\RowIterator' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/RowIterator.php',
    'OpenSpout\\Reader\\XLSX\\Sheet' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/Sheet.php',
    'OpenSpout\\Reader\\XLSX\\SheetHeaderReader' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/SheetHeaderReader.php',
    'OpenSpout\\Reader\\XLSX\\SheetIterator' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/SheetIterator.php',
    'OpenSpout\\Reader\\XLSX\\SheetMergeCellsReader' => $vendorDir . '/openspout/openspout/src/Reader/XLSX/SheetMergeCellsReader.php',
    'OpenSpout\\Writer\\AbstractWriter' => $vendorDir . '/openspout/openspout/src/Writer/AbstractWriter.php',
    'OpenSpout\\Writer\\AbstractWriterMultiSheets' => $vendorDir . '/openspout/openspout/src/Writer/AbstractWriterMultiSheets.php',
    'OpenSpout\\Writer\\AutoFilter' => $vendorDir . '/openspout/openspout/src/Writer/AutoFilter.php',
    'OpenSpout\\Writer\\CSV\\Options' => $vendorDir . '/openspout/openspout/src/Writer/CSV/Options.php',
    'OpenSpout\\Writer\\CSV\\Writer' => $vendorDir . '/openspout/openspout/src/Writer/CSV/Writer.php',
    'OpenSpout\\Writer\\Common\\AbstractOptions' => $vendorDir . '/openspout/openspout/src/Writer/Common/AbstractOptions.php',
    'OpenSpout\\Writer\\Common\\ColumnWidth' => $vendorDir . '/openspout/openspout/src/Writer/Common/ColumnWidth.php',
    'OpenSpout\\Writer\\Common\\Creator\\WriterFactory' => $vendorDir . '/openspout/openspout/src/Writer/Common/Creator/WriterFactory.php',
    'OpenSpout\\Writer\\Common\\Entity\\Sheet' => $vendorDir . '/openspout/openspout/src/Writer/Common/Entity/Sheet.php',
    'OpenSpout\\Writer\\Common\\Entity\\Workbook' => $vendorDir . '/openspout/openspout/src/Writer/Common/Entity/Workbook.php',
    'OpenSpout\\Writer\\Common\\Entity\\Worksheet' => $vendorDir . '/openspout/openspout/src/Writer/Common/Entity/Worksheet.php',
    'OpenSpout\\Writer\\Common\\Helper\\CellHelper' => $vendorDir . '/openspout/openspout/src/Writer/Common/Helper/CellHelper.php',
    'OpenSpout\\Writer\\Common\\Helper\\FileSystemWithRootFolderHelperInterface' => $vendorDir . '/openspout/openspout/src/Writer/Common/Helper/FileSystemWithRootFolderHelperInterface.php',
    'OpenSpout\\Writer\\Common\\Helper\\ZipHelper' => $vendorDir . '/openspout/openspout/src/Writer/Common/Helper/ZipHelper.php',
    'OpenSpout\\Writer\\Common\\Manager\\AbstractWorkbookManager' => $vendorDir . '/openspout/openspout/src/Writer/Common/Manager/AbstractWorkbookManager.php',
    'OpenSpout\\Writer\\Common\\Manager\\RegisteredStyle' => $vendorDir . '/openspout/openspout/src/Writer/Common/Manager/RegisteredStyle.php',
    'OpenSpout\\Writer\\Common\\Manager\\SheetManager' => $vendorDir . '/openspout/openspout/src/Writer/Common/Manager/SheetManager.php',
    'OpenSpout\\Writer\\Common\\Manager\\Style\\AbstractStyleManager' => $vendorDir . '/openspout/openspout/src/Writer/Common/Manager/Style/AbstractStyleManager.php',
    'OpenSpout\\Writer\\Common\\Manager\\Style\\AbstractStyleRegistry' => $vendorDir . '/openspout/openspout/src/Writer/Common/Manager/Style/AbstractStyleRegistry.php',
    'OpenSpout\\Writer\\Common\\Manager\\Style\\PossiblyUpdatedStyle' => $vendorDir . '/openspout/openspout/src/Writer/Common/Manager/Style/PossiblyUpdatedStyle.php',
    'OpenSpout\\Writer\\Common\\Manager\\Style\\StyleManagerInterface' => $vendorDir . '/openspout/openspout/src/Writer/Common/Manager/Style/StyleManagerInterface.php',
    'OpenSpout\\Writer\\Common\\Manager\\Style\\StyleMerger' => $vendorDir . '/openspout/openspout/src/Writer/Common/Manager/Style/StyleMerger.php',
    'OpenSpout\\Writer\\Common\\Manager\\WorkbookManagerInterface' => $vendorDir . '/openspout/openspout/src/Writer/Common/Manager/WorkbookManagerInterface.php',
    'OpenSpout\\Writer\\Common\\Manager\\WorksheetManagerInterface' => $vendorDir . '/openspout/openspout/src/Writer/Common/Manager/WorksheetManagerInterface.php',
    'OpenSpout\\Writer\\Exception\\Border\\InvalidNameException' => $vendorDir . '/openspout/openspout/src/Writer/Exception/Border/InvalidNameException.php',
    'OpenSpout\\Writer\\Exception\\Border\\InvalidStyleException' => $vendorDir . '/openspout/openspout/src/Writer/Exception/Border/InvalidStyleException.php',
    'OpenSpout\\Writer\\Exception\\Border\\InvalidWidthException' => $vendorDir . '/openspout/openspout/src/Writer/Exception/Border/InvalidWidthException.php',
    'OpenSpout\\Writer\\Exception\\InvalidSheetNameException' => $vendorDir . '/openspout/openspout/src/Writer/Exception/InvalidSheetNameException.php',
    'OpenSpout\\Writer\\Exception\\SheetNotFoundException' => $vendorDir . '/openspout/openspout/src/Writer/Exception/SheetNotFoundException.php',
    'OpenSpout\\Writer\\Exception\\WriterAlreadyOpenedException' => $vendorDir . '/openspout/openspout/src/Writer/Exception/WriterAlreadyOpenedException.php',
    'OpenSpout\\Writer\\Exception\\WriterException' => $vendorDir . '/openspout/openspout/src/Writer/Exception/WriterException.php',
    'OpenSpout\\Writer\\Exception\\WriterNotOpenedException' => $vendorDir . '/openspout/openspout/src/Writer/Exception/WriterNotOpenedException.php',
    'OpenSpout\\Writer\\ODS\\Helper\\BorderHelper' => $vendorDir . '/openspout/openspout/src/Writer/ODS/Helper/BorderHelper.php',
    'OpenSpout\\Writer\\ODS\\Helper\\FileSystemHelper' => $vendorDir . '/openspout/openspout/src/Writer/ODS/Helper/FileSystemHelper.php',
    'OpenSpout\\Writer\\ODS\\Manager\\Style\\StyleManager' => $vendorDir . '/openspout/openspout/src/Writer/ODS/Manager/Style/StyleManager.php',
    'OpenSpout\\Writer\\ODS\\Manager\\Style\\StyleRegistry' => $vendorDir . '/openspout/openspout/src/Writer/ODS/Manager/Style/StyleRegistry.php',
    'OpenSpout\\Writer\\ODS\\Manager\\WorkbookManager' => $vendorDir . '/openspout/openspout/src/Writer/ODS/Manager/WorkbookManager.php',
    'OpenSpout\\Writer\\ODS\\Manager\\WorksheetManager' => $vendorDir . '/openspout/openspout/src/Writer/ODS/Manager/WorksheetManager.php',
    'OpenSpout\\Writer\\ODS\\Options' => $vendorDir . '/openspout/openspout/src/Writer/ODS/Options.php',
    'OpenSpout\\Writer\\ODS\\Writer' => $vendorDir . '/openspout/openspout/src/Writer/ODS/Writer.php',
    'OpenSpout\\Writer\\WriterInterface' => $vendorDir . '/openspout/openspout/src/Writer/WriterInterface.php',
    'OpenSpout\\Writer\\XLSX\\Entity\\SheetView' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Entity/SheetView.php',
    'OpenSpout\\Writer\\XLSX\\Helper\\BorderHelper' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Helper/BorderHelper.php',
    'OpenSpout\\Writer\\XLSX\\Helper\\DateHelper' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Helper/DateHelper.php',
    'OpenSpout\\Writer\\XLSX\\Helper\\DateIntervalHelper' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Helper/DateIntervalHelper.php',
    'OpenSpout\\Writer\\XLSX\\Helper\\FileSystemHelper' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Helper/FileSystemHelper.php',
    'OpenSpout\\Writer\\XLSX\\Helper\\PasswordHashHelper' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Helper/PasswordHashHelper.php',
    'OpenSpout\\Writer\\XLSX\\Manager\\CommentsManager' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Manager/CommentsManager.php',
    'OpenSpout\\Writer\\XLSX\\Manager\\SharedStringsManager' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Manager/SharedStringsManager.php',
    'OpenSpout\\Writer\\XLSX\\Manager\\Style\\StyleManager' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Manager/Style/StyleManager.php',
    'OpenSpout\\Writer\\XLSX\\Manager\\Style\\StyleRegistry' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Manager/Style/StyleRegistry.php',
    'OpenSpout\\Writer\\XLSX\\Manager\\WorkbookManager' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Manager/WorkbookManager.php',
    'OpenSpout\\Writer\\XLSX\\Manager\\WorksheetManager' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Manager/WorksheetManager.php',
    'OpenSpout\\Writer\\XLSX\\MergeCell' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/MergeCell.php',
    'OpenSpout\\Writer\\XLSX\\Options' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Options.php',
    'OpenSpout\\Writer\\XLSX\\Options\\HeaderFooter' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Options/HeaderFooter.php',
    'OpenSpout\\Writer\\XLSX\\Options\\PageMargin' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Options/PageMargin.php',
    'OpenSpout\\Writer\\XLSX\\Options\\PageOrientation' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Options/PageOrientation.php',
    'OpenSpout\\Writer\\XLSX\\Options\\PageSetup' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Options/PageSetup.php',
    'OpenSpout\\Writer\\XLSX\\Options\\PaperSize' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Options/PaperSize.php',
    'OpenSpout\\Writer\\XLSX\\Options\\SheetProtection' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Options/SheetProtection.php',
    'OpenSpout\\Writer\\XLSX\\Options\\WorkbookProtection' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Options/WorkbookProtection.php',
    'OpenSpout\\Writer\\XLSX\\Properties' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Properties.php',
    'OpenSpout\\Writer\\XLSX\\Writer' => $vendorDir . '/openspout/openspout/src/Writer/XLSX/Writer.php',
    'quark\\Quark' => $baseDir . '/Quark.php',
);
