@echo off
setlocal enabledelayedexpansion

echo ========================================
echo     夸克网盘工具桌面版 - 开发模式
echo ========================================
echo.

:: 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Node.js，请先安装Node.js
    pause
    exit /b 1
)

:: 检查依赖
if not exist "node_modules" (
    echo [信息] 首次运行，正在安装依赖...
    call npm install
    if %errorlevel% neq 0 (
        echo [错误] 依赖安装失败
        pause
        exit /b 1
    )
)

echo [信息] 启动开发模式...
echo.
echo 应用将在几秒钟后启动
echo 如需停止，请按 Ctrl+C
echo.

:: 启动应用
call npm start
