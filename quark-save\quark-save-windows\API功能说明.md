# 短剧API功能说明

## 🎉 新增功能

我们为夸克网盘自动化工具添加了全新的**短剧API功能**，让您能够自动获取最新的短剧资源并转存到网盘中！

## 🌟 功能特色

### 📡 API接口集成
- **数据源**：`duanju.click` 短剧API
- **实时更新**：每日最新短剧资源
- **智能搜索**：支持关键词精确搜索
- **自动转存**：获取后可自动保存到网盘

### 🔧 四大核心功能

#### 1. 📅 今日更新
- **功能**：获取当天最新更新的短剧
- **用法**：点击"今日更新"按钮
- **命令行**：`php QuarkService.php --options api_today`

#### 2. 📋 全部短剧
- **功能**：获取完整的短剧资源列表
- **用法**：点击"全部短剧"按钮
- **命令行**：`php QuarkService.php --options api_list`

#### 3. 🔍 API搜索
- **功能**：根据关键词搜索相关短剧
- **用法**：点击"API搜索"按钮，输入关键词
- **命令行**：`php QuarkService.php --options api_search --search "关键词"`

#### 4. ⚡ 自动更新
- **功能**：获取今日更新并自动转存到网盘
- **用法**：点击"自动更新"按钮
- **命令行**：`php QuarkService.php --options api_auto_update`

## 🎯 使用方法

### Web界面操作
1. 启动程序：双击 `启动夸克工具.bat`
2. 配置Cookie：点击"设置" → 配置夸克网盘Cookie
3. 使用API功能：在主页面的"短剧API功能"区域选择相应功能

### 命令行操作
```bash
# 获取今日更新
php QuarkService.php --options api_today

# 获取全部短剧
php QuarkService.php --options api_list

# 搜索短剧
php QuarkService.php --options api_search --search "霸道总裁"

# 自动更新（获取+转存）
php QuarkService.php --options api_auto_update
```

## ⏰ 定时任务功能

### 自动创建定时任务
1. 以管理员身份运行 `创建定时任务.bat`
2. 系统将创建每日上午9点的自动更新任务
3. 任务会自动获取当日更新并转存到网盘

### 手动执行定时任务
- 双击 `每日自动更新.bat` 立即执行更新
- 或使用命令：`schtasks /run /tn "QuarkDailyUpdate"`

## 📁 文件说明

### 新增文件
- `DuanjuApi.php` - API接口封装类
- `每日自动更新.bat` - 定时任务执行脚本
- `创建定时任务.bat` - 定时任务创建脚本
- `API功能说明.md` - 本说明文件

### 生成文件
- `api_today_YYYY-MM-DD.txt` - 今日更新数据
- `api_list_YYYY-MM-DD_HH-mm-ss.txt` - 全部短剧数据
- `api_search_关键词_YYYY-MM-DD_HH-mm-ss.txt` - 搜索结果数据

## 🔧 高级配置

### API设置
在配置页面可以调整：
- **API超时时间**：默认30秒
- **自动转存开关**：获取后是否自动保存
- **请求间隔**：避免频繁请求被限制

### 网络异常处理
- **智能降级**：网络不可用时使用演示数据
- **错误重试**：自动重试失败的请求
- **状态监控**：实时显示API连接状态

## 📊 API数据格式

### 返回格式
```json
{
  "code": 200,
  "msg": "今日更新",
  "data": [
    {
      "name": "短剧名称",
      "link": "https://pan.quark.cn/s/xxxxxx",
      "time": "2025-06-14 23:00:00"
    }
  ]
}
```

### 保存格式
```
短剧名称1 https://pan.quark.cn/s/xxxxxx
短剧名称2 https://pan.quark.cn/s/yyyyyy
```

## 🚀 使用建议

### 最佳实践
1. **每日更新**：建议使用定时任务每天自动更新
2. **关键词搜索**：使用具体的短剧名称或演员名搜索
3. **批量转存**：先获取数据，再批量转存，避免频繁操作
4. **网络稳定**：确保网络连接稳定，提高成功率

### 注意事项
- API有访问频率限制，请适当控制请求间隔
- 转存操作需要有效的夸克网盘Cookie
- 建议在网络空闲时段进行大批量操作
- 定时任务需要管理员权限创建

## 🔄 版本更新

### v2.1 新增功能
- ✅ 短剧API接口集成
- ✅ 四种API操作模式
- ✅ 自动转存功能
- ✅ 定时任务支持
- ✅ 网络异常处理
- ✅ Web界面优化

### 兼容性
- 完全兼容原有功能
- 支持命令行和Web界面双模式
- 向下兼容所有配置文件

## 📞 技术支持

- **项目主页**：https://github.com/henggedaren/quark-save
- **API提供**：duanju.click
- **功能开发**：AI Assistant

---

**享受全新的API自动化体验！** 🎊
