# 夸克网盘工具桌面版 - 使用说明

## 📋 概述

这是夸克网盘自动化工具的桌面版本，将原有的Web应用打包成Windows可执行文件，提供更好的用户体验。

## 🚀 快速开始

### 方法一：直接使用（推荐）

1. **安装Node.js**
   - 访问 [https://nodejs.org](https://nodejs.org) 下载并安装Node.js LTS版本
   - 安装完成后重启命令提示符

2. **运行开发版本**
   ```bash
   # 双击运行
   start-dev.bat
   
   # 或者手动执行
   npm install
   npm start
   ```

3. **构建可执行文件**
   ```bash
   # 双击运行
   build.bat
   
   # 或者手动执行
   npm run build-win
   ```

### 方法二：手动设置

1. **环境准备**
   ```bash
   # 检查Node.js版本
   node --version
   
   # 进入项目目录
   cd electron-app
   
   # 安装依赖
   npm install
   ```

2. **开发模式运行**
   ```bash
   npm start
   ```

3. **构建生产版本**
   ```bash
   npm run build-win
   ```

## 📁 项目结构

```
electron-app/
├── main.js              # 主进程文件
├── preload.js           # 预加载脚本
├── loading.html         # 启动加载页面
├── package.json         # 项目配置
├── assets/              # 应用资源
│   ├── icon.ico         # 应用图标（需要创建）
│   ├── icon.png         # 窗口图标（需要创建）
│   └── tray-icon.png    # 托盘图标（需要创建）
├── dist/                # 构建输出目录
├── start-dev.bat        # 开发模式启动脚本
├── build.bat            # 构建脚本
├── setup.bat            # 环境设置脚本
└── create-icons.bat     # 图标创建指南
```

## 🎯 功能特性

### ✅ 已实现功能

- **桌面应用界面** - 原生Windows应用体验
- **系统托盘支持** - 最小化到托盘，后台运行
- **内置PHP运行时** - 无需单独安装PHP环境
- **自动服务器启动** - 自动启动内置Web服务器
- **加载页面** - 优雅的启动加载体验
- **单实例运行** - 防止重复启动
- **外部链接处理** - 自动在默认浏览器中打开链接

### 🔧 系统托盘功能

- **右键菜单**：
  - 显示主窗口
  - 打开配置页面
  - 关于信息
  - 退出应用

- **双击托盘图标**：显示主窗口

### 📱 窗口管理

- **最小化行为**：关闭窗口时最小化到托盘
- **恢复显示**：通过托盘菜单或双击托盘图标
- **完全退出**：通过托盘菜单选择"退出"

## 🛠️ 开发指南

### 环境要求

- **Node.js**: 16.x 或更高版本
- **npm**: 8.x 或更高版本
- **Windows**: Windows 10 或更高版本

### 开发命令

```bash
# 安装依赖
npm install

# 开发模式（热重载）
npm start

# 构建Windows版本
npm run build-win

# 构建所有平台
npm run build

# 仅打包不发布
npm run dist
```

### 调试模式

在开发模式下，可以按 `Ctrl+Shift+I` 打开开发者工具进行调试。

## 📦 构建和分发

### 构建配置

应用使用 `electron-builder` 进行打包，配置在 `package.json` 的 `build` 部分：

- **输出格式**: NSIS安装程序
- **目标架构**: x64
- **包含文件**: PHP运行时、Web文件、依赖库

### 构建输出

构建完成后，在 `dist/` 目录中会生成：

- `夸克网盘工具 Setup 1.0.0.exe` - 安装程序
- `win-unpacked/` - 未打包的应用文件

## 🎨 自定义图标

### 创建图标文件

1. **应用图标** (`assets/icon.ico`)
   - 格式：ICO
   - 尺寸：包含16x16, 32x32, 48x48, 256x256
   - 用途：任务栏、桌面快捷方式

2. **窗口图标** (`assets/icon.png`)
   - 格式：PNG
   - 尺寸：512x512 或 256x256
   - 用途：应用窗口标题栏

3. **托盘图标** (`assets/tray-icon.png`)
   - 格式：PNG
   - 尺寸：32x32 或 16x16
   - 用途：系统托盘显示

### 图标工具推荐

- **在线转换**: [convertio.co](https://convertio.co)
- **图标生成**: [favicon.io](https://favicon.io)
- **图像编辑**: GIMP, Photoshop

## 🐛 故障排除

### 常见问题

1. **Node.js未安装**
   ```
   解决方案：安装Node.js LTS版本
   ```

2. **依赖安装失败**
   ```bash
   # 清理缓存
   npm cache clean --force
   
   # 使用国内镜像
   npm config set registry https://registry.npmmirror.com
   
   # 重新安装
   npm install
   ```

3. **PHP服务器启动失败**
   ```
   检查：php/php.exe 文件是否存在
   确保：防火墙允许本地端口8080
   ```

4. **构建失败**
   ```bash
   # 检查磁盘空间
   # 确保有足够的内存
   # 关闭杀毒软件的实时保护
   ```

### 日志查看

- **开发模式**: 控制台输出
- **生产模式**: 应用数据目录中的日志文件

## 📞 技术支持

如果遇到问题，请：

1. 查看控制台错误信息
2. 检查 `logs/` 目录中的日志文件
3. 确认所有依赖文件完整
4. 尝试重新构建应用

## 📄 许可证

MIT License - 详见 LICENSE 文件
