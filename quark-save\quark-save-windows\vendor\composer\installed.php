<?php return array(
    'root' => array(
        'name' => 'henggedaren/quark-save',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '0b9c373d8ce4cbca8a7d490ab3a33d9a4f5aec8a',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'henggedaren/quark-save' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '0b9c373d8ce4cbca8a7d490ab3a33d9a4f5aec8a',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'openspout/openspout' => array(
            'pretty_version' => 'v4.30.0',
            'version' => '4.30.0.0',
            'reference' => 'df9b0f4d229c37c3caa5a9252a6ad8a94efb0fb5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../openspout/openspout',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/console_table' => array(
            'pretty_version' => 'v1.3.1',
            'version' => '1.3.1.0',
            'reference' => '1930c11897ca61fd24b95f2f785e99e0f36dcdea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pear/console_table',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
