<?php

/**
 * 日志管理类
 * 统一管理各种类型的日志文件和目录结构
 */
class LogManager
{
    private $baseDir;
    private $logTypes = [
        'api' => 'API相关日志',
        'system' => '系统操作日志',
        'share' => '分享操作日志',
        'sync' => '同步操作日志'
    ];

    public function __construct($baseDir = './logs')
    {
        $this->baseDir = rtrim($baseDir, '/\\');
        $this->initDirectories();
    }

    /**
     * 初始化日志目录结构
     */
    private function initDirectories()
    {
        if (!is_dir($this->baseDir)) {
            mkdir($this->baseDir, 0755, true);
        }

        foreach (array_keys($this->logTypes) as $type) {
            $dir = $this->baseDir . DIRECTORY_SEPARATOR . $type;
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }

    /**
     * 获取API日志文件路径
     * @param string $operation 操作类型 (today, list, search)
     * @param string $keyword 搜索关键词（可选）
     * @return string
     */
    public function getApiLogPath($operation, $keyword = '')
    {
        $date = date('Y-m-d');
        $time = date('H-i-s');
        
        switch ($operation) {
            case 'today':
                $filename = "today_{$date}.txt";
                break;
            case 'list':
                $filename = "list_{$date}_{$time}.txt";
                break;
            case 'search':
                $safeKeyword = $this->sanitizeFilename($keyword);
                $filename = "search_{$safeKeyword}_{$date}_{$time}.txt";
                break;
            default:
                $filename = "{$operation}_{$date}_{$time}.txt";
        }

        return $this->baseDir . DIRECTORY_SEPARATOR . 'api' . DIRECTORY_SEPARATOR . $filename;
    }

    /**
     * 获取系统日志文件路径
     * @param string $operation 操作类型
     * @return string
     */
    public function getSystemLogPath($operation)
    {
        $date = date('Y-m-d');
        $filename = "{$operation}_{$date}.log";
        return $this->baseDir . DIRECTORY_SEPARATOR . 'system' . DIRECTORY_SEPARATOR . $filename;
    }

    /**
     * 获取分享日志文件路径
     * @param string $fid 文件夹ID
     * @return string
     */
    public function getShareLogPath($fid)
    {
        $date = date('Y-m-d');
        $filename = "share_{$fid}_{$date}.txt";
        return $this->baseDir . DIRECTORY_SEPARATOR . 'share' . DIRECTORY_SEPARATOR . $filename;
    }

    /**
     * 获取同步日志文件路径
     * @param string $fid 文件夹ID
     * @return string
     */
    public function getSyncLogPath($fid)
    {
        $date = date('Y-m-d');
        $filename = "sync_{$fid}_{$date}.txt";
        return $this->baseDir . DIRECTORY_SEPARATOR . 'sync' . DIRECTORY_SEPARATOR . $filename;
    }

    /**
     * 清理过期日志文件
     * @param int $days 保留天数
     */
    public function cleanOldLogs($days = 30)
    {
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        
        foreach (array_keys($this->logTypes) as $type) {
            $dir = $this->baseDir . DIRECTORY_SEPARATOR . $type;
            if (!is_dir($dir)) continue;
            
            $files = glob($dir . DIRECTORY_SEPARATOR . '*');
            foreach ($files as $file) {
                if (is_file($file) && filemtime($file) < $cutoffTime) {
                    unlink($file);
                }
            }
        }
    }

    /**
     * 获取日志统计信息
     * @return array
     */
    public function getLogStats()
    {
        $stats = [];
        
        foreach (array_keys($this->logTypes) as $type) {
            $dir = $this->baseDir . DIRECTORY_SEPARATOR . $type;
            $files = glob($dir . DIRECTORY_SEPARATOR . '*');
            $stats[$type] = [
                'count' => count($files),
                'size' => 0,
                'latest' => null
            ];
            
            $latestTime = 0;
            foreach ($files as $file) {
                if (is_file($file)) {
                    $stats[$type]['size'] += filesize($file);
                    $mtime = filemtime($file);
                    if ($mtime > $latestTime) {
                        $latestTime = $mtime;
                        $stats[$type]['latest'] = date('Y-m-d H:i:s', $mtime);
                    }
                }
            }
            
            $stats[$type]['size'] = $this->formatBytes($stats[$type]['size']);
        }
        
        return $stats;
    }

    /**
     * 安全化文件名
     * @param string $filename
     * @return string
     */
    private function sanitizeFilename($filename)
    {
        // 移除或替换不安全的字符
        $filename = preg_replace('/[^\w\-_\.]/', '_', $filename);
        // 限制长度
        return substr($filename, 0, 50);
    }

    /**
     * 格式化字节大小
     * @param int $bytes
     * @return string
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * 创建日志目录的README文件
     */
    public function createReadme()
    {
        $readmeContent = "# 日志目录说明\n\n";
        $readmeContent .= "本目录用于存储夸克网盘自动化工具的各种日志文件。\n\n";
        $readmeContent .= "## 目录结构\n\n";
        
        foreach ($this->logTypes as $type => $description) {
            $readmeContent .= "- `{$type}/` - {$description}\n";
        }
        
        $readmeContent .= "\n## 文件命名规则\n\n";
        $readmeContent .= "- API日志：`api/today_YYYY-MM-DD.txt`、`api/search_关键词_YYYY-MM-DD_HH-mm-ss.txt`\n";
        $readmeContent .= "- 系统日志：`system/操作类型_YYYY-MM-DD.log`\n";
        $readmeContent .= "- 分享日志：`share/share_文件夹ID_YYYY-MM-DD.txt`\n";
        $readmeContent .= "- 同步日志：`sync/sync_文件夹ID_YYYY-MM-DD.txt`\n";
        $readmeContent .= "\n## 日志清理\n\n";
        $readmeContent .= "系统会自动清理30天前的日志文件，如需保留请手动备份。\n";
        
        file_put_contents($this->baseDir . DIRECTORY_SEPARATOR . 'README.md', $readmeContent);
    }

    /**
     * 获取基础目录
     * @return string
     */
    public function getBaseDir()
    {
        return $this->baseDir;
    }
}
?>
