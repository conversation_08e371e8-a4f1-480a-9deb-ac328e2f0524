[PHP]
; Basic PHP configuration for quark-save project

; Enable extensions
extension_dir = "ext"
extension=curl
extension=openssl
extension=mbstring
extension=fileinfo
extension=zip

; Memory and execution settings
memory_limit = 512M
max_execution_time = 300

; Error reporting
error_reporting = E_ALL & ~E_NOTICE & ~E_STRICT & ~E_DEPRECATED
display_errors = Off
log_errors = On

; File uploads
file_uploads = On
upload_max_filesize = 100M
post_max_size = 100M

; Date settings
date.timezone = "Asia/Shanghai"

; SSL/TLS settings
openssl.cafile = "extras/ssl/cacert.pem"
curl.cainfo = "extras/ssl/cacert.pem"

; Other useful settings
allow_url_fopen = On
allow_url_include = Off
