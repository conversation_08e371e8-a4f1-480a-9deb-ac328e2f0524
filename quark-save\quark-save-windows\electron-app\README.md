# 夸克网盘工具桌面版

这是夸克网盘自动化工具的桌面版本，使用 Electron 技术构建。

## 功能特性

- 🖥️ 桌面应用界面，使用方便
- 🔄 系统托盘支持，后台运行
- 🚀 内置PHP运行时，无需额外安装
- 📱 响应式界面，适配不同屏幕尺寸
- 🔧 完整的配置管理功能
- 📊 实时日志查看

## 开发和构建

### 安装依赖

```bash
cd electron-app
npm install
```

### 开发模式运行

```bash
npm start
```

### 构建Windows可执行文件

```bash
npm run build-win
```

构建完成后，可执行文件将在 `dist` 目录中生成。

## 项目结构

```
electron-app/
├── main.js          # 主进程文件
├── preload.js       # 预加载脚本
├── package.json     # 项目配置
├── assets/          # 应用资源
│   ├── icon.ico     # 应用图标
│   ├── icon.png     # 窗口图标
│   └── tray-icon.png # 托盘图标
└── dist/            # 构建输出目录
```

## 使用说明

1. 双击运行生成的 `.exe` 文件
2. 应用会自动启动内置的PHP服务器
3. 浏览器窗口会自动打开并加载应用界面
4. 可以通过系统托盘图标控制应用的显示/隐藏
5. 关闭窗口时应用会最小化到托盘，不会完全退出

## 注意事项

- 首次启动可能需要几秒钟时间来启动PHP服务器
- 确保防火墙允许应用访问网络（本地端口8080）
- 如果遇到问题，可以查看控制台输出获取详细信息

## 技术栈

- Electron - 桌面应用框架
- PHP - 后端服务
- HTML/CSS/JavaScript - 前端界面
- Node.js - 构建工具
