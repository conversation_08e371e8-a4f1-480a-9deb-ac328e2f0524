@echo off
setlocal enabledelayedexpansion

:: Set script directory
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

:: Set PHP path
set "PHP_PATH=%SCRIPT_DIR%php\php.exe"

:: Check if P<PERSON> exists
if not exist "%PHP_PATH%" (
    echo Error: PHP runtime not found!
    echo Please ensure php directory exists and contains php.exe file.
    pause
    exit /b 1
)

:: Display menu
:menu
cls
echo ========================================
echo        Quark Cloud Disk Tool
echo ========================================
echo.
echo Please select an operation:
echo.
echo 1. Auto Sign-in
echo 2. Save Resources (need file path)
echo 3. Share Resources
echo 4. Sync Directory
echo 5. Auto Update All Dramas
echo 6. Auto Update Daily New Dramas
echo 7. Search and Save Drama
echo 8. Exit
echo.
set /p choice=Please enter option (1-8):

if "%choice%"=="1" goto sign
if "%choice%"=="2" goto save
if "%choice%"=="3" goto share
if "%choice%"=="4" goto syn_dir
if "%choice%"=="5" goto auto_all
if "%choice%"=="6" goto auto_daily
if "%choice%"=="7" goto search
if "%choice%"=="8" goto exit
echo Invalid option, please try again!
pause
goto menu

:sign
echo.
echo Executing auto sign-in...
"%PHP_PATH%" QuarkService.php --options sign
pause
goto menu

:save
echo.
set /p file_path=Enter file path (supports txt,csv,xlsx,xls):
if "%file_path%"=="" (
    echo File path cannot be empty!
    pause
    goto menu
)
echo Saving resources...
"%PHP_PATH%" QuarkService.php --options save --path "%file_path%"
pause
goto menu

:share
echo.
echo Sharing resources...
"%PHP_PATH%" QuarkService.php --options share
pause
goto menu

:syn_dir
echo.
echo Syncing directory...
"%PHP_PATH%" QuarkService.php --options syn_dir
pause
goto menu

:auto_all
echo.
echo Auto updating all dramas...
"%PHP_PATH%" QuarkService.php --options auto --update all
pause
goto menu

:auto_daily
echo.
echo Auto updating daily new dramas...
"%PHP_PATH%" QuarkService.php --options auto --update daily
pause
goto menu

:search
echo.
set /p drama_name=Enter drama name to search:
if "%drama_name%"=="" (
    echo Drama name cannot be empty!
    pause
    goto menu
)
echo Searching and saving drama...
"%PHP_PATH%" QuarkService.php --options auto --name "%drama_name%"
pause
goto menu

:exit
echo Thank you for using Quark Cloud Disk Tool!
exit /b 0
