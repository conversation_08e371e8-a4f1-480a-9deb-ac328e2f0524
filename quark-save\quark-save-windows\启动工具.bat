@echo off
title Quark Tool - Quick Start

echo.
echo ========================================
echo         Quark Cloud Disk Tool
echo            Quick Start Mode
echo ========================================
echo.

:: Set script directory
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

:: Check PHP runtime
if not exist "php\php.exe" (
    echo [ERROR] PHP runtime not found!
    echo Please ensure php\php.exe exists
    pause
    exit /b 1
)

echo [INFO] PHP runtime check completed
echo [INFO] Starting Quark Cloud Disk Tool...
echo.
echo Access URLs:
echo   Main page: http://127.0.0.1:6666
echo   Config page: http://127.0.0.1:6666/config
echo.
echo Usage tips:
echo   1. Configure Cookie on first use
echo   2. Press Ctrl+C to stop service
echo   3. Close this window to stop all services
echo.
echo ========================================
echo.

:: Auto open browser
start "" "http://127.0.0.1:6666"

:: Start PHP server
php\php.exe -S 127.0.0.1:6666 -t . server.php

echo.
echo ========================================
echo Service stopped
echo ========================================
pause
