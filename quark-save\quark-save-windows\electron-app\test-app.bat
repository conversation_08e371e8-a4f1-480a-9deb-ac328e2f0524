@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

title 夸克网盘工具桌面版 - 功能测试

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    夸克网盘工具桌面版                        ║
echo ║                      功能测试脚本                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 设置脚本目录
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo 🧪 开始应用功能测试...
echo.

:: 测试1: 检查Node.js环境
echo [测试 1/8] Node.js环境检查
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装
    set "TEST1=FAIL"
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js版本: !NODE_VERSION!
    set "TEST1=PASS"
)

:: 测试2: 检查项目依赖
echo [测试 2/8] 项目依赖检查
if not exist "node_modules" (
    echo ❌ 依赖未安装
    set "TEST2=FAIL"
) else (
    echo ✅ 依赖已安装
    set "TEST2=PASS"
)

:: 测试3: 检查核心文件
echo [测试 3/8] 核心文件检查
set "CORE_FILES=main.js preload.js loading.html package.json"
set "TEST3=PASS"
for %%f in (!CORE_FILES!) do (
    if not exist "%%f" (
        echo ❌ 缺少文件: %%f
        set "TEST3=FAIL"
    )
)
if "!TEST3!"=="PASS" (
    echo ✅ 核心文件完整
)

:: 测试4: 检查PHP运行时
echo [测试 4/8] PHP运行时检查
if not exist "..\php\php.exe" (
    echo ❌ PHP运行时未找到
    set "TEST4=FAIL"
) else (
    echo ✅ PHP运行时存在
    set "TEST4=PASS"
)

:: 测试5: 检查Web文件
echo [测试 5/8] Web文件检查
if not exist "..\web\index.html" (
    echo ❌ Web界面文件未找到
    set "TEST5=FAIL"
) else (
    echo ✅ Web界面文件存在
    set "TEST5=PASS"
)

:: 测试6: 检查服务器脚本
echo [测试 6/8] 服务器脚本检查
if not exist "..\server.php" (
    echo ❌ 服务器脚本未找到
    set "TEST6=FAIL"
) else (
    echo ✅ 服务器脚本存在
    set "TEST6=PASS"
)

:: 测试7: 检查配置文件
echo [测试 7/8] 配置文件检查
if not exist "..\cookie.txt" (
    echo ⚠️  Cookie配置文件不存在（首次运行正常）
    set "TEST7=WARN"
) else (
    echo ✅ Cookie配置文件存在
    set "TEST7=PASS"
)

:: 测试8: 端口可用性检查
echo [测试 8/8] 端口可用性检查
netstat -an | findstr ":8080" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  端口8080已被占用
    set "TEST8=WARN"
) else (
    echo ✅ 端口8080可用
    set "TEST8=PASS"
)

:: 汇总测试结果
echo.
echo ════════════════════════════════════════════════════════════════
echo 📊 测试结果汇总
echo ════════════════════════════════════════════════════════════════

set "PASS_COUNT=0"
set "FAIL_COUNT=0"
set "WARN_COUNT=0"

for %%t in (TEST1 TEST2 TEST3 TEST4 TEST5 TEST6 TEST7 TEST8) do (
    if "!%%t!"=="PASS" set /a PASS_COUNT+=1
    if "!%%t!"=="FAIL" set /a FAIL_COUNT+=1
    if "!%%t!"=="WARN" set /a WARN_COUNT+=1
)

echo ✅ 通过: !PASS_COUNT!/8
echo ❌ 失败: !FAIL_COUNT!/8
echo ⚠️  警告: !WARN_COUNT!/8

echo.
if !FAIL_COUNT! equ 0 (
    echo 🎉 所有关键测试通过！应用可以正常运行。
    echo.
    echo 建议的下一步操作：
    echo 1. 运行 "一键运行.bat" 启动应用
    echo 2. 运行 "build.bat" 构建可执行文件
    echo 3. 配置Cookie后开始使用
) else (
    echo ⚠️  发现 !FAIL_COUNT! 个问题，请先解决后再运行应用。
    echo.
    echo 常见解决方案：
    echo - 安装Node.js: https://nodejs.org
    echo - 运行 "setup.bat" 安装依赖
    echo - 确保PHP文件完整
)

if !WARN_COUNT! gtr 0 (
    echo.
    echo 💡 警告信息说明：
    if "!TEST7!"=="WARN" echo - Cookie文件：首次运行时不存在是正常的
    if "!TEST8!"=="WARN" echo - 端口占用：可能需要关闭其他使用8080端口的程序
)

echo.
echo 是否现在启动应用进行实际测试？[Y/N]
set /p choice=
if /i "!choice!"=="Y" (
    if !FAIL_COUNT! equ 0 (
        echo 🚀 启动应用...
        call npm start
    ) else (
        echo ❌ 请先解决失败的测试项目
    )
)

echo.
pause
