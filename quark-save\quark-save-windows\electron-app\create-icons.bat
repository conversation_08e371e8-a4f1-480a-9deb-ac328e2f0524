@echo off
echo ========================================
echo        创建应用图标文件
echo ========================================
echo.
echo 此脚本将帮助您创建应用所需的图标文件
echo.
echo 需要创建的图标文件:
echo 1. assets/icon.ico     - 应用主图标 (Windows ICO格式)
echo 2. assets/icon.png     - 窗口图标 (PNG格式, 512x512)
echo 3. assets/tray-icon.png - 托盘图标 (PNG格式, 32x32)
echo.
echo 您可以:
echo 1. 使用在线图标生成器 (如 favicon.io, convertio.co)
echo 2. 使用图像编辑软件 (如 GIMP, Photoshop)
echo 3. 从现有图片转换
echo.
echo 建议的图标设计:
echo - 使用夸克网盘的蓝色主题色
echo - 简洁的"Q"字母或云朵图标
echo - 在小尺寸下保持清晰
echo.
echo 如果暂时没有图标，应用仍可正常构建和运行
echo 只是会使用默认的Electron图标
echo.
pause
