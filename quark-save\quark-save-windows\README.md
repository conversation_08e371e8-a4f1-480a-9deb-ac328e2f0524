# Quark Cloud Disk Automation Tool - Windows Executable

## Overview
This is a Windows executable version of the Quark Cloud Disk automation tool that supports automatic sign-in, resource saving, sharing, and other functions.

## Quick Start

### 1. Get Quark Cloud Disk Cookie
1. Open your browser and log in to Quark Cloud Disk (https://pan.quark.cn)
2. Press F12 to open Developer Tools
3. Switch to the "Network" tab
4. Refresh the page
5. Find any request in the request list and check the Cookie in the request headers
6. Copy the complete Cookie content
7. Save the Cookie content to the `cookie.txt` file in the program directory

### 2. Run the Program
Double-click `quark-save.bat` to start the program

## Features

### 1. Auto Sign-in
- Automatically complete daily Quark Cloud Disk sign-in
- Display account information and sign-in status

### 2. Save Resources
- Support batch saving resources from txt, csv, xlsx, xls files
- File format: Each line contains "Resource Name Quark Link"
- Example:
  ```
  Movie Name 1 https://pan.quark.cn/s/xxxxxx
  Movie Name 2 https://pan.quark.cn/s/yyyyyy
  ```

### 3. Share Resources
- Batch share folders in cloud disk
- Need to execute "Sync Directory" function first

### 4. Sync Directory
- Sync cloud disk directory structure to local
- Prepare for sharing function

### 5. Auto Update All Dramas
- Get all drama resources from cloud API and auto save

### 6. Auto Update Daily New Dramas
- Get daily new drama resources from cloud API and auto save

### 7. Search and Save Drama
- Search dramas by keywords and auto save

## Important Notes

1. Must configure cookie.txt file before first use
2. Cookie has expiration time, please re-obtain if login fails
3. Save operations have frequency limits, recommend appropriate intervals
4. Program automatically handles network request intervals to avoid restrictions

## File Structure
```
quark-save-windows/
├── quark-save.bat          # Main startup program
├── QuarkService.php        # Core function script
├── Quark.php              # Quark API wrapper
├── common.php             # Common functions
├── composer.json          # Dependency configuration
├── cookie.txt             # Cookie configuration file (need manual creation)
├── php/                   # PHP runtime
├── vendor/                # Dependency libraries
├── 使用说明.txt           # Chinese instructions
└── README.md              # This file
```

## Troubleshooting

### Q: Shows "Invalid cookie" error?
A: Re-obtain Cookie and update cookie.txt file

### Q: Save operation fails?
A: Check network connection, confirm links are valid, reduce operation frequency

### Q: Program won't start?
A: Ensure all files are complete, especially files in php directory

## Technical Support
For issues, please visit the project homepage: https://github.com/henggedaren/quark-save

## License
This project is licensed under the Apache-2.0 License.
