@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

title 夸克网盘工具桌面版 - 一键运行

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    夸克网盘工具桌面版                        ║
echo ║                      一键运行脚本                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 设置脚本目录
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

:: 检查Node.js
echo [1/5] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ 未检测到Node.js！
    echo.
    echo 请按照以下步骤安装Node.js：
    echo 1. 访问 https://nodejs.org
    echo 2. 下载并安装LTS版本
    echo 3. 重启命令提示符后重新运行此脚本
    echo.
    echo 是否现在打开Node.js官网？[Y/N]
    set /p choice=
    if /i "!choice!"=="Y" (
        start https://nodejs.org
    )
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: !NODE_VERSION!

:: 检查npm
echo [2/5] 检查npm包管理器...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm未找到，请重新安装Node.js
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm版本: !NPM_VERSION!

:: 检查并安装依赖
echo [3/5] 检查项目依赖...
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖包...
    echo 这可能需要几分钟时间，请耐心等待...
    echo.
    
    call npm install
    if %errorlevel% neq 0 (
        echo.
        echo ❌ 依赖安装失败！
        echo.
        echo 可能的解决方案：
        echo 1. 检查网络连接
        echo 2. 尝试使用国内镜像：
        echo    npm config set registry https://registry.npmmirror.com
        echo 3. 清理npm缓存：
        echo    npm cache clean --force
        echo.
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成！
) else (
    echo ✅ 依赖已安装
)

:: 检查PHP运行时
echo [4/5] 检查PHP运行时...
if exist "..\php\php.exe" (
    echo ✅ PHP运行时已就绪
) else (
    echo ❌ PHP运行时未找到！
    echo 请确保 ..\php\php.exe 文件存在
    pause
    exit /b 1
)

:: 启动应用
echo [5/5] 启动应用...
echo.
echo 🚀 正在启动夸克网盘工具桌面版...
echo.
echo 提示：
echo - 应用启动后会自动打开窗口
echo - 可以最小化到系统托盘
echo - 按 Ctrl+C 可以停止应用
echo.
echo ════════════════════════════════════════════════════════════════
echo.

:: 启动Electron应用
call npm start

:: 如果应用意外退出
echo.
echo ════════════════════════════════════════════════════════════════
echo 应用已退出
echo ════════════════════════════════════════════════════════════════
echo.
pause
