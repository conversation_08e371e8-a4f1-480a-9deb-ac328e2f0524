@echo off
setlocal enabledelayedexpansion

:: Set script directory
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

:: Set PHP path
set "PHP_PATH=%SCRIPT_DIR%php\php.exe"

echo ========================================
echo         Log Cleanup Tool
echo ========================================
echo.
echo This tool will clean up old log files to save disk space.
echo.

:: Check if PHP exists
if not exist "%PHP_PATH%" (
    echo Error: PHP runtime not found!
    echo Please ensure php\php.exe exists.
    pause
    exit /b 1
)

:: Show current log statistics
echo Current log statistics:
echo.
"%PHP_PATH%" -r "include 'LogManager.php'; $lm = new LogManager(); $stats = $lm->getLogStats(); foreach($stats as $type => $stat) { echo \"$type: {$stat['count']} files, {$stat['size']}\n\"; }"
echo.

:: Ask for confirmation
set /p days=Enter number of days to keep (default 30): 
if "%days%"=="" set days=30

echo.
echo This will delete log files older than %days% days.
set /p confirm=Are you sure? (Y/N): 

if /i "%confirm%" neq "Y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

:: Clean old logs
echo.
echo Cleaning old log files...
"%PHP_PATH%" -r "include 'LogManager.php'; $lm = new LogManager(); $lm->cleanOldLogs(%days%); echo 'Log cleanup completed.\n';"

:: Show updated statistics
echo.
echo Updated log statistics:
"%PHP_PATH%" -r "include 'LogManager.php'; $lm = new LogManager(); $stats = $lm->getLogStats(); foreach($stats as $type => $stat) { echo \"$type: {$stat['count']} files, {$stat['size']}\n\"; }"

echo.
echo Log cleanup completed successfully!
pause
