# 夸克网盘自动化工具 - Web界面版

## 🎉 全新Web界面版本

这是一个全新的Web界面版本，提供了美观的图形界面和更好的用户体验！

## ✨ 主要特性

### 🖥️ 美观的Web界面
- 现代化的响应式设计
- 渐变背景和毛玻璃效果
- 直观的卡片式布局
- 实时状态显示

### 🔧 功能完整
- **基础功能**：自动签到、转存资源、分享资源、同步目录
- **高级功能**：自动更新短剧、搜索转存
- **配置管理**：Cookie配置、系统设置
- **实时日志**：操作过程实时显示

### 📱 用户友好
- 一键启动，自动打开浏览器
- 图形化配置界面
- 操作进度实时反馈
- 桌面通知支持

## 🚀 快速开始

### 1. 启动程序
双击 `启动夸克工具.bat` 文件，程序会：
- 自动启动Web服务器
- 打开浏览器访问 http://localhost:8080
- 显示美观的主界面

### 2. 配置Cookie
1. 点击右上角的"设置"按钮
2. 按照页面提示获取夸克网盘Cookie
3. 将Cookie粘贴到文本框中
4. 点击"保存配置"

### 3. 开始使用
返回主页面，选择需要的功能：
- **自动签到**：一键完成每日签到
- **转存资源**：批量转存分享链接
- **分享资源**：批量分享网盘文件
- **更新短剧**：自动获取最新短剧资源

## 📋 界面说明

### 主界面
- **状态卡片**：显示系统状态和Cookie配置状态
- **基础功能区**：常用的签到、转存、分享功能
- **高级功能区**：短剧更新和搜索功能
- **操作日志**：实时显示操作过程和结果

### 配置页面
- **Cookie配置**：设置夸克网盘登录凭证
- **系统信息**：查看PHP版本、服务状态等
- **高级设置**：请求间隔、重试次数等参数
- **系统操作**：导入导出配置、重置设置

## 🔧 高级功能

### 文件转存
1. 点击"转存资源"按钮
2. 选择本地文件或输入文件路径
3. 支持格式：txt、csv、xlsx、xls
4. 文件格式：每行包含"资源名称 夸克链接"

### 短剧搜索
1. 点击"搜索短剧"按钮
2. 输入短剧名称关键词
3. 系统自动搜索并转存匹配的资源

### 配置管理
- **导出配置**：将Cookie和设置保存为JSON文件
- **导入配置**：从JSON文件恢复配置
- **重置配置**：清空所有设置回到初始状态

## 🛠️ 技术特性

### 架构设计
- **前端**：Bootstrap 5 + 原生JavaScript
- **后端**：PHP内置Web服务器
- **通信**：RESTful API + AJAX
- **存储**：本地文件 + localStorage

### 安全性
- 本地运行，数据不上传
- Cookie仅存储在本地
- 支持配置导入导出备份

### 兼容性
- 支持所有现代浏览器
- 响应式设计，支持移动设备
- Windows 7/8/10/11 兼容

## 📁 文件结构

```
quark-save-windows/
├── 启动夸克工具.bat        # 主启动程序
├── server.php             # Web服务器路由
├── QuarkService.php       # 核心功能脚本
├── Quark.php             # 夸克API封装
├── common.php            # 公共函数
├── cookie.txt            # Cookie配置文件
├── php/                  # PHP运行时
├── vendor/               # 依赖库
└── web/                  # Web界面文件
    ├── index.html        # 主界面
    ├── config.html       # 配置页面
    ├── style.css         # 样式文件
    ├── app.js           # 主界面脚本
    └── config.js        # 配置页面脚本
```

## ❓ 常见问题

### Q: 浏览器无法打开页面？
A: 检查防火墙设置，确保允许PHP程序访问网络

### Q: 提示"Cookie无效"？
A: 重新获取Cookie，确保复制完整的Cookie字符串

### Q: 操作失败怎么办？
A: 查看操作日志了解具体错误，检查网络连接和Cookie有效性

### Q: 如何停止服务器？
A: 在启动窗口按Ctrl+C或直接关闭命令行窗口

## 🔄 版本对比

| 功能 | 命令行版本 | Web界面版本 |
|------|-----------|------------|
| 界面美观度 | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| 操作便捷性 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 功能完整性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 配置管理 | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| 实时反馈 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

## 📞 技术支持

- 项目主页：https://github.com/henggedaren/quark-save
- 原作者：henggedaren
- Web界面开发：AI Assistant

---

**享受全新的Web界面体验！** 🎊
