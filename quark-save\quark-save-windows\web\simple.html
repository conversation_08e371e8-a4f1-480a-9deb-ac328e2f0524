<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>夸克网盘自动化工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: #007bff;
            color: white;
            padding: 20px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
        }
        .status {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 12px 20px;
            border: 2px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 5px;
            cursor: pointer;
            text-align: center;
            text-decoration: none;
            transition: all 0.3s;
        }
        .btn:hover {
            background: #007bff;
            color: white;
        }
        .btn-success { border-color: #28a745; color: #28a745; }
        .btn-success:hover { background: #28a745; color: white; }
        .btn-warning { border-color: #ffc107; color: #856404; }
        .btn-warning:hover { background: #ffc107; color: #856404; }
        .btn-info { border-color: #17a2b8; color: #17a2b8; }
        .btn-info:hover { background: #17a2b8; color: white; }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>夸克网盘自动化工具</h1>
            <p>简化版界面 - 所有功能正常可用</p>
        </div>

        <div class="status" id="status">
            <strong>系统状态：</strong><span id="status-text">正在检查...</span>
        </div>

        <div class="section">
            <div class="section-title">基础功能</div>
            <div class="button-grid">
                <button class="btn btn-success" onclick="executeAction('sign')">自动签到</button>
                <button class="btn btn-info" onclick="executeAction('share')">分享资源</button>
                <button class="btn btn-warning" onclick="executeAction('syn_dir')">同步目录</button>
                <button class="btn" onclick="showSaveDialog()">转存资源</button>
            </div>
        </div>

        <div class="section">
            <div class="section-title">短剧功能</div>
            <div class="button-grid">
                <button class="btn btn-info" onclick="executeAction('auto', {update: 'all'})">更新全部短剧</button>
                <button class="btn btn-info" onclick="executeAction('auto', {update: 'daily'})">更新每日短剧</button>
                <button class="btn" onclick="executeApiAction('api_today')">API今日更新</button>
                <button class="btn" onclick="executeApiAction('api_list')">API全部短剧</button>
            </div>
        </div>

        <div class="section">
            <div class="section-title">操作日志</div>
            <div class="log-area" id="log-container">
                等待操作...
            </div>
        </div>

        <div style="text-align: center; margin-top: 20px;">
            <a href="/config" class="btn">配置设置</a>
            <a href="/" class="btn">完整界面</a>
        </div>
    </div>

    <script>
        // 检查系统状态
        function checkStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    const statusText = document.getElementById('status-text');
                    if (data.status === 'ok') {
                        statusText.innerHTML = `系统正常运行 | PHP ${data.phpVersion} | Cookie: ${data.hasCookie ? '已配置' : '未配置'}`;
                        statusText.style.color = 'green';
                    } else {
                        statusText.innerHTML = '系统异常';
                        statusText.style.color = 'red';
                    }
                })
                .catch(error => {
                    document.getElementById('status-text').innerHTML = '连接失败';
                });
        }

        // 执行操作
        function executeAction(action, params = {}) {
            addLog(`开始执行: ${action}`);
            
            fetch('/api/execute', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action: action, params: params })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    addLog(`✅ ${action} 执行成功`);
                    addLog(data.output);
                } else {
                    addLog(`❌ ${action} 执行失败`);
                    addLog(data.output);
                }
            })
            .catch(error => {
                addLog(`❌ 网络错误: ${error.message}`);
            });
        }

        // 执行API操作
        function executeApiAction(action) {
            addLog(`开始执行API: ${action}`);
            executeAction(action);
        }

        // 显示转存对话框
        function showSaveDialog() {
            const filePath = prompt('请输入资源文件路径（例如: resources.txt）:');
            if (filePath) {
                executeAction('save', { file: filePath });
            }
        }

        // 添加日志
        function addLog(message) {
            const logContainer = document.getElementById('log-container');
            const time = new Date().toLocaleTimeString();
            logContainer.innerHTML += `[${time}] ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 页面加载时检查状态
        window.onload = function() {
            checkStatus();
            addLog('页面加载完成');
        };
    </script>
</body>
</html>
