<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>夸克网盘工具 - 启动中</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
        }

        .loading-container {
            text-align: center;
            max-width: 400px;
            padding: 40px;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            font-weight: bold;
        }

        .title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 16px;
            opacity: 0.8;
            margin-bottom: 40px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            margin: 0 auto 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            font-size: 14px;
            opacity: 0.9;
            animation: pulse 2s ease-in-out infinite;
        }

        .status-list {
            margin-top: 30px;
            text-align: left;
        }

        .status-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 14px;
            opacity: 0.8;
        }

        .status-icon {
            width: 16px;
            height: 16px;
            margin-right: 10px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }

        .status-icon.loading {
            animation: pulse 1.5s ease-in-out infinite;
        }

        .status-icon.success {
            background: #4CAF50;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        .version {
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 12px;
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="logo">夸</div>
        <h1 class="title">夸克网盘工具</h1>
        <p class="subtitle">桌面版正在启动...</p>
        
        <div class="loading-spinner"></div>
        <p class="loading-text">正在初始化服务...</p>
        
        <div class="status-list">
            <div class="status-item">
                <div class="status-icon loading">●</div>
                <span>启动PHP运行时</span>
            </div>
            <div class="status-item">
                <div class="status-icon loading">●</div>
                <span>初始化Web服务器</span>
            </div>
            <div class="status-item">
                <div class="status-icon loading">●</div>
                <span>加载应用界面</span>
            </div>
        </div>
    </div>
    
    <div class="version">v1.0.0</div>

    <script>
        // 模拟加载进度
        let currentStep = 0;
        const steps = document.querySelectorAll('.status-icon');
        
        function updateProgress() {
            if (currentStep < steps.length) {
                steps[currentStep].classList.remove('loading');
                steps[currentStep].classList.add('success');
                steps[currentStep].textContent = '✓';
                currentStep++;
                
                if (currentStep < steps.length) {
                    setTimeout(updateProgress, 1500);
                }
            }
        }
        
        // 开始进度更新
        setTimeout(updateProgress, 1000);
        
        // 更新加载文本
        const loadingTexts = [
            '正在初始化服务...',
            '正在启动PHP服务器...',
            '正在加载应用界面...',
            '即将完成...'
        ];
        
        let textIndex = 0;
        const loadingTextElement = document.querySelector('.loading-text');
        
        setInterval(() => {
            textIndex = (textIndex + 1) % loadingTexts.length;
            loadingTextElement.textContent = loadingTexts[textIndex];
        }, 2000);
    </script>
</body>
</html>
