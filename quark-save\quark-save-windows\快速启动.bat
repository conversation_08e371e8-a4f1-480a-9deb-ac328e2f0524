@echo off
chcp 65001 >nul
title 夸克网盘工具 - 快速启动

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    夸克网盘自动化工具                        ║
echo ║                      快速启动模式                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 设置脚本目录
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

:: 检查PHP运行时
if not exist "php\php.exe" (
    echo ❌ PHP运行时未找到！
    echo 请确保 php\php.exe 文件存在
    pause
    exit /b 1
)

echo ✅ PHP运行时检查完成
echo.
echo 🚀 正在启动夸克网盘工具...
echo.
echo 📍 访问地址：
echo    主页面: http://localhost:6666
echo    配置页面: http://localhost:6666/config
echo.
echo 💡 使用提示：
echo    1. 首次使用请先配置Cookie
echo    2. 按 Ctrl+C 可停止服务
echo    3. 关闭此窗口将停止所有服务
echo.
echo ════════════════════════════════════════════════════════════════
echo.

:: 自动打开浏览器
start "" "http://localhost:6666"

:: 启动PHP服务器
php\php.exe -S localhost:6666 -t "%SCRIPT_DIR%" server.php

echo.
echo ════════════════════════════════════════════════════════════════
echo 服务已停止
echo ════════════════════════════════════════════════════════════════
pause
