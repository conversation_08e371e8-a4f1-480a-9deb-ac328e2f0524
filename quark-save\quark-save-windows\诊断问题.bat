@echo off
title 夸克网盘工具 - 问题诊断

echo.
echo ========================================
echo         问题诊断和解决方案
echo ========================================
echo.

echo [1/6] 检查PHP运行时...
if exist "php\php.exe" (
    echo ✅ PHP运行时存在
    php\php.exe --version | findstr "PHP"
) else (
    echo ❌ PHP运行时不存在
    goto :error
)

echo.
echo [2/6] 检查Web文件...
if exist "web\index.html" (
    echo ✅ 主页面文件存在
) else (
    echo ❌ 主页面文件不存在
    goto :error
)

if exist "web\simple.html" (
    echo ✅ 简化页面文件存在
) else (
    echo ❌ 简化页面文件不存在
)

echo.
echo [3/6] 检查端口占用...
netstat -an | findstr :6666 >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  端口6666已被占用
    echo 占用情况：
    netstat -ano | findstr :6666
    echo.
    echo 请先关闭占用端口的程序，或者：
    echo 1. 按任意键继续强制启动
    echo 2. 按Ctrl+C退出
    pause >nul
) else (
    echo ✅ 端口6666可用
)

echo.
echo [4/6] 启动测试服务器...
echo 正在启动PHP服务器...
start /B php\php.exe -S 127.0.0.1:6666 -t . server.php

echo 等待服务器启动...
timeout /t 3 >nul

echo.
echo [5/6] 测试服务器响应...
php\php.exe -r "try { echo file_get_contents('http://127.0.0.1:6666/api/status'); } catch(Exception $e) { echo 'Error: ' . $e->getMessage(); }" >test_result.txt 2>&1

if exist test_result.txt (
    echo 服务器响应：
    type test_result.txt
    del test_result.txt
) else (
    echo ❌ 无法测试服务器响应
)

echo.
echo [6/6] 浏览器测试...
echo 正在打开浏览器测试页面...
echo.
echo 测试地址：
echo   简化版页面: http://127.0.0.1:6666/simple.html
echo   完整版页面: http://127.0.0.1:6666/
echo   API状态: http://127.0.0.1:6666/api/status
echo   配置页面: http://127.0.0.1:6666/config
echo.

start "" "http://127.0.0.1:6666/simple.html"

echo ========================================
echo 诊断完成！
echo ========================================
echo.
echo 如果简化版页面可以正常访问，说明服务器工作正常。
echo 如果完整版页面无法访问，可能是以下原因：
echo.
echo 1. 浏览器缓存问题 - 按Ctrl+F5强制刷新
echo 2. 外部CDN资源加载失败 - 检查网络连接
echo 3. 防火墙阻止 - 临时关闭防火墙测试
echo 4. 杀毒软件拦截 - 添加程序到白名单
echo.
echo 建议使用简化版页面进行操作：
echo http://127.0.0.1:6666/simple.html
echo.
echo 按任意键退出...
pause >nul

:error
echo.
echo 发现错误，请检查文件完整性
pause
exit /b 1
