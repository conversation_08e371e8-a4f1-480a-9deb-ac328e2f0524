@echo off
setlocal enabledelayedexpansion

echo Creating Quark-Save Windows Release Package...
echo.

:: Set directories
set "SOURCE_DIR=%~dp0"
set "RELEASE_DIR=%SOURCE_DIR%quark-save-windows"

:: Clean up previous release
if exist "%RELEASE_DIR%" (
    echo Cleaning up previous release...
    rmdir /s /q "%RELEASE_DIR%"
)

:: Create release directory
mkdir "%RELEASE_DIR%"

echo Copying files...

:: Copy main files
copy "%SOURCE_DIR%quark-save.bat" "%RELEASE_DIR%\"
copy "%SOURCE_DIR%QuarkService.php" "%RELEASE_DIR%\"
copy "%SOURCE_DIR%Quark.php" "%RELEASE_DIR%\"
copy "%SOURCE_DIR%common.php" "%RELEASE_DIR%\"
copy "%SOURCE_DIR%composer.json" "%RELEASE_DIR%\"
copy "%SOURCE_DIR%cookie.txt" "%RELEASE_DIR%\"
copy "%SOURCE_DIR%使用说明.txt" "%RELEASE_DIR%\"
copy "%SOURCE_DIR%LICENSE" "%RELEASE_DIR%\"
copy "%SOURCE_DIR%README.md" "%RELEASE_DIR%\"

:: Copy PHP runtime
echo Copying PHP runtime...
xcopy "%SOURCE_DIR%php" "%RELEASE_DIR%\php" /e /i /h /y

:: Copy vendor directory
echo Copying dependencies...
xcopy "%SOURCE_DIR%vendor" "%RELEASE_DIR%\vendor" /e /i /h /y

:: Copy original batch files for reference
echo Copying original batch files...
copy "%SOURCE_DIR%1、自动签到.bat" "%RELEASE_DIR%\original-scripts\" 2>nul
copy "%SOURCE_DIR%2、同步目录.bat" "%RELEASE_DIR%\original-scripts\" 2>nul
copy "%SOURCE_DIR%3、自动分享.bat" "%RELEASE_DIR%\original-scripts\" 2>nul
copy "%SOURCE_DIR%4、自动更新全部短剧.bat" "%RELEASE_DIR%\original-scripts\" 2>nul
copy "%SOURCE_DIR%5、自动更新每日新增短剧.bat" "%RELEASE_DIR%\original-scripts\" 2>nul

:: Create sample files directory
mkdir "%RELEASE_DIR%\samples"

:: Create sample resource file
echo Creating sample files...
echo 电影名称1 https://pan.quark.cn/s/xxxxxx > "%RELEASE_DIR%\samples\sample-resources.txt"
echo 电影名称2 https://pan.quark.cn/s/yyyyyy >> "%RELEASE_DIR%\samples\sample-resources.txt"
echo 电影名称3 https://pan.quark.cn/s/zzzzzz >> "%RELEASE_DIR%\samples\sample-resources.txt"

:: Clean up unnecessary files
echo Cleaning up unnecessary files...
del "%RELEASE_DIR%\php-8.3.22-nts-Win32-vs16-x64.zip" 2>nul
del "%RELEASE_DIR%\composer.phar" 2>nul
del "%RELEASE_DIR%\create-release.bat" 2>nul

echo.
echo Release package created successfully!
echo Location: %RELEASE_DIR%
echo.
echo You can now distribute the 'quark-save-windows' folder.
echo Users should run 'quark-save.bat' to start the application.
echo.
pause
