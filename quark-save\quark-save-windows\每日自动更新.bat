@echo off
setlocal enabledelayedexpansion

:: Set script directory
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

:: Set PHP path
set "PHP_PATH=%SCRIPT_DIR%php\php.exe"

:: Check if <PERSON><PERSON> exists
if not exist "%PHP_PATH%" (
    echo Error: PHP runtime not found!
    echo Please ensure php\php.exe exists.
    exit /b 1
)

:: Display startup information
echo ========================================
echo         Daily Auto Update Task
echo ========================================
echo.
echo Starting daily auto update...
echo Time: %date% %time%
echo.

:: Execute API auto update
"%PHP_PATH%" QuarkService.php --options api_auto_update

:: Check result
if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo         Update Completed Successfully
    echo ========================================
) else (
    echo.
    echo ========================================
    echo         Update Failed
    echo ========================================
)

echo.
echo Task finished at: %date% %time%
echo.

:: If running manually, pause to see results
if "%1" neq "auto" (
    pause
)
