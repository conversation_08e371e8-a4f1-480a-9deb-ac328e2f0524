const { app, BrowserWindow, <PERSON>u, Tray, shell, ipcMain, dialog } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');
const http = require('http');

let mainWindow;
let tray;
let phpServer;
const serverPort = 8080;

// 确保只有一个实例运行
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
} else {
    app.on('second-instance', () => {
        // 当运行第二个实例时，将焦点放在主窗口上
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });

    // 当 Electron 完成初始化时创建窗口
    app.whenReady().then(() => {
        createWindow();
        createTray();
        startPhpServer();
    });
}

function createWindow() {
    // 创建浏览器窗口
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        },
        icon: path.join(__dirname, 'assets', 'icon.png'),
        title: '夸克网盘工具',
        show: false // 先不显示，等加载完成后再显示
    });

    // 窗口准备好后显示
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
    });

    // 先显示加载页面
    mainWindow.loadFile(path.join(__dirname, 'loading.html'));

    // 等待PHP服务器启动后加载主应用
    setTimeout(() => {
        checkServerAndLoad();
    }, 2000);

    // 处理窗口关闭事件
    mainWindow.on('close', (event) => {
        if (!app.isQuiting) {
            event.preventDefault();
            mainWindow.hide();
            
            // 显示托盘提示
            if (tray) {
                tray.displayBalloon({
                    iconType: 'info',
                    title: '夸克网盘工具',
                    content: '应用已最小化到系统托盘'
                });
            }
        }
    });

    // 处理外部链接
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });
}

function checkServerAndLoad() {
    const checkServer = () => {
        const req = http.get(`http://localhost:${serverPort}/api/status`, (res) => {
            if (res.statusCode === 200) {
                // 服务器已就绪，加载主应用
                mainWindow.loadURL(`http://localhost:${serverPort}`);
            } else {
                // 服务器未就绪，继续等待
                setTimeout(checkServer, 1000);
            }
        });

        req.on('error', () => {
            // 连接失败，继续等待
            setTimeout(checkServer, 1000);
        });

        req.setTimeout(2000, () => {
            req.destroy();
            setTimeout(checkServer, 1000);
        });
    };

    checkServer();
}

function createTray() {
    // 创建系统托盘
    const iconPath = path.join(__dirname, 'assets', 'tray-icon.png');
    tray = new Tray(iconPath);
    
    const contextMenu = Menu.buildFromTemplate([
        {
            label: '显示主窗口',
            click: () => {
                mainWindow.show();
                mainWindow.focus();
            }
        },
        {
            label: '打开配置页面',
            click: () => {
                shell.openExternal(`http://localhost:${serverPort}/config`);
            }
        },
        { type: 'separator' },
        {
            label: '关于',
            click: () => {
                dialog.showMessageBox(mainWindow, {
                    type: 'info',
                    title: '关于夸克网盘工具',
                    message: '夸克网盘自动化工具桌面版',
                    detail: '版本: 1.0.0\n\n这是一个用于自动化管理夸克网盘的桌面应用程序。'
                });
            }
        },
        {
            label: '退出',
            click: () => {
                app.isQuiting = true;
                app.quit();
            }
        }
    ]);
    
    tray.setToolTip('夸克网盘工具');
    tray.setContextMenu(contextMenu);
    
    // 双击托盘图标显示窗口
    tray.on('double-click', () => {
        mainWindow.show();
        mainWindow.focus();
    });
}

function startPhpServer() {
    const phpPath = getPhpPath();
    const appBasePath = getAppBasePath();
    const serverScript = path.join(appBasePath, 'server.php');
    const workingDir = appBasePath;

    console.log('Starting PHP server...');
    console.log('PHP Path:', phpPath);
    console.log('Server Script:', serverScript);
    console.log('Working Directory:', workingDir);
    console.log('App is packaged:', app.isPackaged);
    
    if (!fs.existsSync(phpPath)) {
        console.error('PHP executable not found:', phpPath);
        dialog.showErrorBox('错误', 'PHP运行时未找到，请确保php目录存在');
        return;
    }
    
    phpServer = spawn(phpPath, [
        '-S', `localhost:${serverPort}`,
        '-t', workingDir,
        serverScript
    ], {
        cwd: workingDir,
        stdio: ['ignore', 'pipe', 'pipe']
    });
    
    phpServer.stdout.on('data', (data) => {
        console.log('PHP Server:', data.toString());
    });
    
    phpServer.stderr.on('data', (data) => {
        console.error('PHP Server Error:', data.toString());
    });
    
    phpServer.on('close', (code) => {
        console.log(`PHP server exited with code ${code}`);
    });
    
    phpServer.on('error', (err) => {
        console.error('Failed to start PHP server:', err);
        dialog.showErrorBox('错误', '无法启动PHP服务器: ' + err.message);
    });
}

function getPhpPath() {
    // 在开发环境中
    if (!app.isPackaged) {
        return path.join(__dirname, '..', 'php', 'php.exe');
    }

    // 在打包后的应用中，PHP文件在extraResources中
    return path.join(process.resourcesPath, 'php', 'php.exe');
}

function getAppBasePath() {
    // 在开发环境中
    if (!app.isPackaged) {
        return path.join(__dirname, '..');
    }

    // 在打包后的应用中，应用文件在app.asar中
    return path.join(__dirname, '..');
}

// 应用退出时清理
app.on('before-quit', () => {
    app.isQuiting = true;
    
    if (phpServer) {
        phpServer.kill();
    }
});

app.on('window-all-closed', () => {
    // 在 macOS 上，除非用户用 Cmd + Q 确定地退出，
    // 否则绝大部分应用及其菜单栏会保持激活。
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    // 在macOS上，当单击dock图标并且没有其他窗口打开时，
    // 通常在应用程序中重新创建一个窗口。
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// IPC 处理程序
ipcMain.handle('get-app-version', () => {
    return app.getVersion();
});

ipcMain.handle('show-message-box', async (event, options) => {
    const result = await dialog.showMessageBox(mainWindow, options);
    return result;
});
