# 夸克网盘自动化工具 - 使用说明

## 📋 功能介绍

这是一个夸克网盘的自动化管理工具，支持以下功能：
- 🔄 自动签到获取积分
- 📁 自动同步目录结构
- 🔗 自动分享文件链接
- 📺 自动更新短剧资源
- 🌐 Web界面管理

## 🚀 快速开始

### 方法一：一键启动（推荐）
```
双击运行：启动工具.bat
```

### 方法二：完整启动
```
双击运行：启动夸克工具.bat
```

### 方法三：手动启动
```
1. 确保 php/php.exe 存在
2. 双击运行：quark-save.bat
```

## 🌐 Web界面访问

启动后自动打开浏览器，或手动访问：
- **主页面**: http://127.0.0.1:6666
- **配置页面**: http://127.0.0.1:6666/config

## ⚙️ 初次配置

### 1. 获取Cookie
1. 打开浏览器访问 https://pan.quark.cn
2. 登录您的夸克账号
3. 按F12打开开发者工具
4. 切换到Network标签
5. 刷新页面，找到任意请求
6. 复制Cookie值

### 2. 配置Cookie
1. 访问配置页面：http://localhost:6666/config
2. 粘贴Cookie到输入框
3. 点击保存

## 🔧 功能使用

### 自动化脚本
- `1、自动签到.bat` - 每日自动签到
- `2、同步目录.bat` - 同步网盘目录
- `3、自动分享.bat` - 批量分享文件
- `4、自动更新全部短剧.bat` - 更新所有短剧
- `5、自动更新每日新增短剧.bat` - 更新新增短剧

### 定时任务
运行 `创建定时任务.bat` 可以设置自动执行计划

### 日志管理
- 查看日志：`logs/` 目录
- 清理日志：运行 `清理日志.bat`

## 📁 目录结构

```
quark-save-windows/
├── php/                    # PHP运行环境
├── web/                    # Web界面文件
├── logs/                   # 日志文件
├── vendor/                 # 依赖库
├── *.php                   # 核心PHP文件
├── *.bat                   # 批处理脚本
├── server.php              # Web服务器
├── cookie.txt              # Cookie配置
└── 使用说明.md             # 本文件
```

## ❓ 常见问题

### Q: 提示"PHP运行时未找到"
A: 确保 `php/php.exe` 文件存在且完整

### Q: 无法访问Web界面
A: 检查端口6666是否被占用，或防火墙是否阻止

### Q: Cookie失效
A: 重新获取Cookie并在配置页面更新

### Q: 自动化脚本不工作
A: 检查Cookie是否正确配置，查看logs目录中的错误日志

## 🔒 安全说明

- Cookie信息仅存储在本地
- 不会上传任何个人信息
- 建议定期更新Cookie

## 📞 技术支持

如遇问题：
1. 查看 `logs/system/` 目录中的错误日志
2. 确认Cookie配置正确
3. 检查网络连接状态

## 🎯 高级功能

### API接口
- 状态检查：GET /api/status
- 配置管理：GET/POST /api/config
- 执行任务：POST /api/execute

### 命令行使用
```bash
php QuarkService.php --options signin
php QuarkService.php --options sync
php QuarkService.php --options share
```

---

**版本**: v2.0  
**更新时间**: 2025-06-15  
**端口**: 6666
