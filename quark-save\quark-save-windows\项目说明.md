# 夸克网盘自动化工具 - Windows可执行版本

## 项目概述

本项目成功将原始的PHP命令行工具转换为Windows可执行程序，用户无需安装PHP环境即可直接使用。

## 完成的工作

### 1. 环境准备
- ✅ 下载并配置PHP 8.3.22便携版
- ✅ 下载并配置Composer包管理器
- ✅ 配置PHP扩展（OpenSSL、cURL、mbstring等）

### 2. 依赖管理
- ✅ 创建完整的composer.json配置文件
- ✅ 安装所需依赖包：
  - pear/console_table (命令行表格显示)
  - openspout/openspout (Excel文件处理)
- ✅ 修改代码以兼容新的依赖库

### 3. 用户界面
- ✅ 创建友好的批处理菜单界面
- ✅ 支持中英文显示
- ✅ 提供8个主要功能选项

### 4. 功能完善
- ✅ 改进Cookie读取逻辑，支持注释行
- ✅ 优化错误提示和用户引导
- ✅ 创建示例文件和详细说明

### 5. 打包发布
- ✅ 创建自包含的发布包
- ✅ 包含所有必要的运行时文件
- ✅ 提供完整的使用说明文档

## 文件结构

```
quark-save-windows/
├── quark-save.bat          # 主启动程序（双击运行）
├── QuarkService.php        # 核心功能脚本
├── Quark.php              # 夸克API封装
├── common.php             # 公共函数库
├── composer.json          # 依赖配置
├── cookie.txt             # Cookie配置文件
├── sample-resources.txt   # 示例资源文件
├── README.md              # 英文说明
├── 使用说明.txt           # 中文说明
├── 项目说明.md            # 项目技术说明
├── php/                   # PHP运行时环境
│   ├── php.exe           # PHP解释器
│   ├── php.ini           # PHP配置
│   ├── ext/              # PHP扩展
│   └── extras/ssl/       # SSL证书
└── vendor/                # 依赖库
    ├── autoload.php      # 自动加载
    ├── openspout/        # Excel处理库
    └── pear/             # 表格显示库
```

## 主要功能

1. **自动签到** - 每日自动签到获取容量
2. **转存资源** - 批量转存分享链接到网盘
3. **分享资源** - 批量分享网盘文件夹
4. **同步目录** - 同步网盘目录结构
5. **更新短剧** - 自动获取并转存短剧资源
6. **搜索转存** - 按关键词搜索并转存

## 技术特点

- **自包含** - 无需安装PHP环境
- **便携性** - 可直接复制到任意Windows系统运行
- **用户友好** - 图形化菜单界面，操作简单
- **功能完整** - 保留原项目所有功能
- **错误处理** - 完善的错误提示和用户引导

## 使用方法

1. 获取夸克网盘Cookie并保存到cookie.txt
2. 双击quark-save.bat启动程序
3. 根据菜单提示选择相应功能
4. 按照提示完成操作

## 系统要求

- Windows 7/8/10/11 (64位)
- 网络连接
- 有效的夸克网盘账号

## 注意事项

- Cookie有时效性，失效后需重新获取
- 转存操作有频率限制，建议适当间隔
- 首次使用前请仔细阅读使用说明

## 技术支持

项目主页：https://github.com/henggedaren/quark-save
原作者：henggedaren
打包制作：AI Assistant
