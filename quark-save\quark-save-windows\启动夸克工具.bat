@echo off
setlocal enabledelayedexpansion

:: Set script directory
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

:: Set PHP path
set "PHP_PATH=%SCRIPT_DIR%php\php.exe"

:: Check if <PERSON><PERSON> exists
if not exist "%PHP_PATH%" (
    echo.
    echo ========================================
    echo           Error: PHP runtime not found
    echo ========================================
    echo.
    echo Please ensure the following files exist:
    echo - php\php.exe
    echo - php\php.ini
    echo.
    echo If files are missing, please re-download the complete package.
    echo.
    pause
    exit /b 1
)

:: Display startup information
cls
echo.
echo ========================================
echo         Quark Cloud Disk Tool v2.0
echo ========================================
echo.
echo Starting Web Server...
echo.
echo Service URL: http://localhost:8080
echo Config Page: http://localhost:8080/config
echo.
echo Please wait, browser will open automatically...
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

:: Start PHP built-in server
start "" "http://localhost:8080"
"%PHP_PATH%" -S localhost:8080 -t "%SCRIPT_DIR%" "%SCRIPT_DIR%server.php"

:: If server stops unexpectedly
echo.
echo ========================================
echo            Server Stopped
echo ========================================
echo.
pause
