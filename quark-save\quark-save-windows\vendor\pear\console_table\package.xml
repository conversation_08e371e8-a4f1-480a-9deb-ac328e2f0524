<?xml version="1.0" encoding="UTF-8"?>
<package packagerversion="1.9.1" version="2.0" xmlns="http://pear.php.net/dtd/package-2.0" xmlns:tasks="http://pear.php.net/dtd/tasks-1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://pear.php.net/dtd/tasks-1.0 http://pear.php.net/dtd/tasks-1.0.xsd http://pear.php.net/dtd/package-2.0 http://pear.php.net/dtd/package-2.0.xsd">
 <name>Console_Table</name>
 <channel>pear.php.net</channel>
 <summary>Library that makes it easy to build console style tables</summary>
 <description>Provides a Console_Table class with methods such as addRow(), insertRow(), addCol() etc. to build console tables with or without headers and with user defined table rules, padding, and alignment.</description>
 <lead>
  <name>Jan <PERSON></name>
  <user>yunosh</user>
  <email><EMAIL></email>
  <active>yes</active>
 </lead>
 <lead>
  <name>Richard Heyes</name>
  <user>richard</user>
  <email><EMAIL></email>
  <active>no</active>
 </lead>
 <developer>
  <name>Tal Peer</name>
  <user>tal</user>
  <email><EMAIL></email>
  <active>no</active>
 </developer>
 <developer>
  <name>Xavier Noguer</name>
  <user>xnoguer</user>
  <email><EMAIL></email>
  <active>no</active>
 </developer>
 <date>2018-01-25</date>
 <version>
  <release>1.3.1</release>
  <api>1.3.0</api>
 </version>
 <stability>
  <release>stable</release>
  <api>stable</api>
 </stability>
 <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
 <notes>
* Fix warning with PHP 7.2 when passing non-array data (Remi Collet &lt;<EMAIL>&gt;, PR #14).
 </notes>
 <contents>
  <dir baseinstalldir="Console" name="/">
   <dir name="tests">
    <file name="assoziative_arrays.phpt" role="test" />
    <file name="border-ascii.phpt" role="test" />
    <file name="border-custom.phpt" role="test" />
    <file name="border-custom2.phpt" role="test" />
    <file name="border-disable.phpt" role="test" />
    <file name="border-dot.phpt" role="test" />
    <file name="border-empty.phpt" role="test" />
    <file name="bug20181.phpt" role="test" />
    <file name="colors.phpt" role="test" />
    <file name="filters.phpt" role="test" />
    <file name="multibyte.phpt" role="test" />
    <file name="multiline.phpt" role="test" />
    <file name="no_header.phpt" role="test" />
    <file name="no_rows.phpt" role="test" />
    <file name="rules.phpt" role="test" />
   </dir>
   <file name="Table.php" role="php" />
  </dir>
 </contents>
 <dependencies>
  <required>
   <php>
    <min>5.2.0</min>
   </php>
   <pearinstaller>
    <min>1.4.0b1</min>
   </pearinstaller>
  </required>
  <optional>
   <package>
    <name>Console_Color2</name>
    <channel>pear.php.net</channel>
    <min>0.1.2</min>
   </package>
  </optional>
 </dependencies>
 <phprelease/>
 <changelog>
  <release>
   <version>
    <release>0.8</release>
    <api>0.8</api>
   </version>
   <stability>
    <release>beta</release>
    <api>beta</api>
   </stability>
   <date>2002-09-02</date>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Initial release.
   </notes>
  </release>
  <release>
   <version>
    <release>1.0</release>
    <api>1.0.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <date>2003-01-24</date>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Removed a few options and added addData() method.
   </notes>
  </release>
  <release>
   <version>
    <release>1.0.1</release>
    <api>1.0.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <date>2003-03-03</date>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Fixed a caching bug.
   </notes>
  </release>
  <release>
   <version>
    <release>1.0.2</release>
    <api>1.0.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <date>2005-07-16</date>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Added support for column alignment (Michael Richter).
   </notes>
  </release>
  <release>
   <version>
    <release>1.0.3</release>
    <api>1.0.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <date>2006-03-13</date>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Fix cell width calculation if setting header with associative array (Bug #4299).
* Fix fatal reference error with some PHP versions (Bug #5309).
* Fix notice if no data has been provided (Bug #5851).
* Added multibyte support (Requests #2934, Request #7014).
   </notes>
  </release>
  <release>
   <version>
    <release>1.0.4</release>
    <api>1.0.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <date>2006-04-08</date>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Add support for multi-line cells (koto at webworkers dot pl, Request #7017).
   </notes>
  </release>
  <release>
   <version>
    <release>1.0.5</release>
    <api>1.0.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <date>2006-08-28</date>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Allow to specify separator rules in addData().
* Fix warnings when combining separator rules and callback filters (Bug #8566).
   </notes>
  </release>
  <release>
   <version>
    <release>1.0.6</release>
    <api>1.0.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <date>2007-01-19</date>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Add support for multi-line headers (Request #8615).
   </notes>
  </release>
  <release>
   <date>2007-05-17</date>
   <version>
    <release>1.0.7</release>
    <api>1.0.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Fix header height if first data row has more than one line (Bug #11064).
* Fix notice if header is not set.
   </notes>
  </release>
  <release>
   <date>2008-01-09</date>
   <version>
    <release>1.0.8</release>
    <api>1.0.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Fix cell padding with multibyte strings under certain circumstances (Bug #12853).
   </notes>
  </release>
  <release>
   <date>2008-03-28</date>
   <version>
    <release>1.1.0</release>
    <api>1.1.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Add option to set table border character.
* Extend constructor to set table borders, padding, and charset on instantiation.
   </notes>
  </release>
  <release>
   <date>2008-04-09</date>
   <version>
    <release>1.1.1</release>
    <api>1.1.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Fix rendering of multiline rows with cells that contain zeros (Bug #13629).
   </notes>
  </release>
  <release>
   <date>2008-07-27</date>
   <version>
    <release>1.1.2</release>
    <api>1.1.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Don&apos;t render anything if no data has been provided (Bug #14405).
   </notes>
  </release>
  <release>
   <date>2008-10-20</date>
   <version>
    <release>1.1.3</release>
    <api>1.1.1</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Add option to render data with ANSI color codes (Igor Feghali, Request #14835).
   </notes>
  </release>
  <release>
   <version>
    <release>1.1.4</release>
    <api>1.1.1</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <date>2010-10-25</date>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Automatically built QA release.
* Add Console_Color support (Request #14835).

* Improve documentation (Christian Weiske, Bug #15006).
   </notes>
  </release>
  <release>
   <date>2012-12-07</date>
   <time>23:30:07</time>
   <version>
    <release>1.1.5</release>
    <api>1.1.1</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Use mb_strwidth() instead of mb_strlen() to determine lengths of multi-byte strings (Bug #19423).
   </notes>
  </release>
  <release>
   <date>2013-10-12</date>
   <version>
    <release>1.1.6</release>
    <api>1.1.1</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Use line breaks dependent on the current operating system (Bug #20092).
   </notes>
  </release>
  <release>
   <date>2014-02-17</date>
   <version>
    <release>1.2.0</release>
    <api>1.2.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Make border visibility configurable (Christian Weiske, Request #20186).
* Allow to customize all border characters (Christian Weiske, Request #20182).
* Fix notice when using setAlign() on other than first column (Christian Weiske, Bug #20181).
* Use Console_Color2 to avoid notices from PHP 4 code (Christian Weiske, Bug #20188)
   </notes>
  </release>
  <release>
   <date>2014-10-27</date>
   <version>
    <release>1.2.1</release>
    <api>1.2.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Add travis configuration (Christian Weiske).
* Try to autoload Console_Color2 first (Jurgen Rutten, PR #11).
* Fix Composer definition syntax (Rob Loach, PR #9).
   </notes>
  </release>
  <release>
   <date>2016-01-21</date>
   <version>
    <release>1.3.0</release>
    <api>1.3.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Fix warning with PHP 7 and bump required PHP version to 5.2.0 (Pieter Frenssen PR #13).
   </notes>
  </release>
  <release>
   <date>2018-01-25</date>
   <version>
    <release>1.3.1</release>
    <api>1.3.0</api>
   </version>
   <stability>
    <release>stable</release>
    <api>stable</api>
   </stability>
   <license uri="http://www.opensource.org/licenses/bsd-license.php">BSD</license>
   <notes>
* Fix warning with PHP 7.2 when passing non-array data (Remi Collet &lt;<EMAIL>&gt;, PR #14).
   </notes>
  </release>
 </changelog>
</package>
