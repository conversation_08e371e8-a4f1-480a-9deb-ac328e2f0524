{"name": "quark-save-desktop", "version": "1.0.0", "description": "夸克网盘自动化工具桌面版", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "dist": "electron-builder --publish=never"}, "keywords": ["quark", "cloud", "automation", "desktop"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "dependencies": {"node-pty": "^1.0.0", "tree-kill": "^1.2.2"}, "build": {"appId": "com.quark.save.desktop", "productName": "夸克网盘工具", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "loading.html", "assets/**/*", "../php/**/*", "../web/**/*", "../*.php", "../*.txt", "../vendor/**/*", "../logs/**/*", "!../php-8.3.22-nts-Win32-vs16-x64.zip"], "extraResources": [{"from": "../php", "to": "php"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "夸克网盘工具"}}}