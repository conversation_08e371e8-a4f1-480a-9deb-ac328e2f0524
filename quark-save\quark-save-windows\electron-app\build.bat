@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

title 夸克网盘工具桌面版 - 构建脚本

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    夸克网盘工具桌面版                        ║
echo ║                      构建打包脚本                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 设置脚本目录
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

:: 检查Node.js环境
echo [1/6] 检查构建环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Node.js，请先安装Node.js
    pause
    exit /b 1
)

:: 检查是否已安装依赖
echo [2/6] 检查项目依赖...
if not exist "node_modules" (
    echo 📦 安装构建依赖...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)
echo ✅ 依赖检查完成

:: 检查PHP运行时
echo [3/6] 检查PHP运行时...
if not exist "..\php\php.exe" (
    echo ❌ PHP运行时未找到，请确保 ..\php\php.exe 存在
    pause
    exit /b 1
)
echo ✅ PHP运行时检查完成

:: 清理之前的构建
echo [4/6] 清理构建目录...
if exist "dist" (
    echo 🧹 清理之前的构建文件...
    rmdir /s /q "dist" 2>nul
)
echo ✅ 构建目录已清理

:: 开始构建
echo [5/6] 开始构建应用...
echo.
echo 🔨 正在构建Windows可执行文件...
echo 这可能需要几分钟时间，请耐心等待...
echo.

call npm run build-win

if %errorlevel% neq 0 (
    echo.
    echo ❌ 构建失败！
    echo.
    echo 可能的原因：
    echo 1. 磁盘空间不足
    echo 2. 杀毒软件阻止
    echo 3. 网络连接问题
    echo 4. 权限不足
    echo.
    echo 建议：
    echo - 关闭杀毒软件的实时保护
    echo - 以管理员身份运行
    echo - 检查磁盘空间
    echo.
    pause
    exit /b 1
)

:: 检查构建结果
echo [6/6] 检查构建结果...
echo.

if exist "dist\*.exe" (
    echo ✅ 构建成功完成！
    echo.
    echo 📁 构建文件位置：
    for %%f in (dist\*.exe) do (
        echo    %%f
        set "EXE_FILE=%%f"
    )
    echo.
    echo 📊 文件信息：
    for %%f in (dist\*.exe) do (
        for %%s in ("%%f") do echo    大小: %%~zs 字节
    )
    echo.
    echo 🎉 您现在可以：
    echo 1. 直接运行 !EXE_FILE!
    echo 2. 将exe文件分发给其他用户
    echo 3. 创建桌面快捷方式
    echo.
    echo 是否现在运行构建的应用？[Y/N]
    set /p choice=
    if /i "!choice!"=="Y" (
        start "" "!EXE_FILE!"
    )
) else (
    echo ❌ 未找到生成的exe文件
    echo.
    echo 请检查构建日志中的错误信息
    if exist "dist" (
        echo.
        echo dist目录内容：
        dir /b "dist"
    )
)

echo.
echo ════════════════════════════════════════════════════════════════
pause
